/**
 * Enhanced Brainstorming Store
 * 
 * Zustand store for managing enhanced brainstorming system state including
 * sessions, ideas, clusters, memories, templates, and UI state.
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import {
  BrainstormSession,
  Idea,
  IdeaCluster,
  IdeaRelationship,
  PersistentMemory,
  BrainstormTemplate,
  AIPersona,
  ViewType,
  IdeaStatus,
  TemplateType,
  SearchQuery,
  SearchResult,
  BrainstormSettings,
  ChatMessage,
  ExportFormat,
  IntegrationConfig,
  BrainstormError
} from '@/types/brainstorm';

// Default personas
const DEFAULT_PERSONAS: AIPersona[] = [
  {
    id: 'default',
    name: 'Balanced Assistant',
    description: 'Balanced perspective with practical insights',
    systemPrompt: 'You are a helpful brainstorming assistant that provides balanced, practical insights.',
    icon: 'brain',
    color: '#6366f1',
    isActive: true
  },
  {
    id: 'optimist',
    name: 'Optimist',
    description: 'Focuses on possibilities and positive outcomes',
    systemPrompt: 'You are an optimistic brainstorming assistant who focuses on possibilities, opportunities, and positive outcomes. Always look for the bright side and potential benefits.',
    icon: 'sun',
    color: '#f59e0b',
    isActive: false
  },
  {
    id: 'critic',
    name: 'Critical Thinker',
    description: 'Identifies potential issues and challenges',
    systemPrompt: 'You are a critical thinking assistant who identifies potential issues, challenges, and risks. Help users think through problems and edge cases.',
    icon: 'alert-triangle',
    color: '#ef4444',
    isActive: false
  },
  {
    id: 'expert',
    name: 'Domain Expert',
    description: 'Provides technical and specialized knowledge',
    systemPrompt: 'You are a domain expert assistant who provides technical, specialized knowledge and industry best practices. Focus on expertise and proven methodologies.',
    icon: 'graduation-cap',
    color: '#10b981',
    isActive: false
  },
  {
    id: 'devil-advocate',
    name: 'Devil\'s Advocate',
    description: 'Challenges assumptions and plays contrarian',
    systemPrompt: 'You are a devil\'s advocate assistant who challenges assumptions, plays contrarian, and helps users think through alternative perspectives and potential counterarguments.',
    icon: 'zap',
    color: '#8b5cf6',
    isActive: false
  }
];

// Default settings
const DEFAULT_SETTINGS: BrainstormSettings = {
  defaultView: ViewType.CHAT,
  autoCluster: true,
  clusterThreshold: 0.7,
  autoExtractIdeas: true,
  voiceEnabled: false,
  voiceLanguage: 'en-US',
  exportFormat: 'markdown',
  maxMemories: 100,
  theme: 'auto',
  notifications: {
    clustering: true,
    exports: true,
    memories: true
  }
};

interface BrainstormStore {
  // Core state
  sessions: Record<string, BrainstormSession>;
  currentSessionId: string | null;
  ideas: Record<string, Idea>;
  clusters: Record<string, IdeaCluster>;
  memories: Record<string, PersistentMemory>;
  templates: Record<string, BrainstormTemplate>;
  personas: AIPersona[];
  relationships: Record<string, IdeaRelationship>;
  
  // UI state
  currentView: ViewType;
  currentPersona: AIPersona;
  isLoading: boolean;
  errors: BrainstormError[];
  
  // Settings
  settings: BrainstormSettings;
  
  // Export and integration
  exportFormats: ExportFormat[];
  integrations: Record<string, IntegrationConfig>;
  
  // Session management
  createSession: (title?: string, template?: TemplateType) => string;
  updateSession: (sessionId: string, updates: Partial<BrainstormSession>) => void;
  deleteSession: (sessionId: string) => void;
  setCurrentSession: (sessionId: string | null) => void;
  getCurrentSession: () => BrainstormSession | null;
  
  // Message management
  addMessage: (sessionId: string, message: Omit<ChatMessage, 'id' | 'timestamp'>) => string;
  updateMessage: (sessionId: string, messageId: string, updates: Partial<ChatMessage>) => void;
  
  // Idea management
  addIdea: (sessionId: string, content: string, options?: Partial<Omit<Idea, 'id' | 'createdAt' | 'updatedAt' | 'content' | 'messageId'>>) => Idea;
  addIdeaFull: (idea: Omit<Idea, 'id' | 'createdAt' | 'updatedAt'>) => string;
  updateIdea: (ideaId: string, updates: Partial<Idea>) => void;
  deleteIdea: (ideaId: string) => void;
  getIdeasBySession: (sessionId: string) => Idea[];
  getIdeasByStatus: (status: IdeaStatus) => Idea[];
  getIdeaById: (ideaId: string) => Idea | undefined;
  
  // Relationship management
  addRelationship: (sourceId: string, targetId: string, type: IdeaRelationship['type'], label?: string) => string;
  removeRelationship: (relationshipId: string) => void;
  getIdeaRelationships: (sessionId: string) => IdeaRelationship[];
  getRelationshipsForIdea: (ideaId: string) => IdeaRelationship[];
  
  // Cluster management
  createCluster: (name: string, theme: string, ideaIds: string[]) => string;
  updateCluster: (clusterId: string, updates: Partial<IdeaCluster>) => void;
  deleteCluster: (clusterId: string) => void;
  addIdeaToCluster: (ideaId: string, clusterId: string) => void;
  removeIdeaFromCluster: (ideaId: string) => void;
  
  // Memory management
  saveMemory: (memory: Omit<PersistentMemory, 'id' | 'createdAt'>) => string;
  updateMemory: (memoryId: string, updates: Partial<PersistentMemory>) => void;
  deleteMemory: (memoryId: string) => void;
  getRelevantMemories: (sessionId: string, query?: string) => PersistentMemory[];
  
  // Template management
  addTemplate: (template: Omit<BrainstormTemplate, 'id' | 'createdAt' | 'updatedAt'>) => string;
  updateTemplate: (templateId: string, updates: Partial<BrainstormTemplate>) => void;
  deleteTemplate: (templateId: string) => void;
  
  // Persona management
  setCurrentPersona: (persona: AIPersona) => void;
  updatePersona: (personaId: string, updates: Partial<AIPersona>) => void;
  
  // View management
  setCurrentView: (view: ViewType) => void;
  
  // Search functionality
  searchSessions: (query: SearchQuery) => SearchResult[];
  
  // Settings management
  updateSettings: (updates: Partial<BrainstormSettings>) => void;
  
  // Error handling
  addError: (error: Omit<BrainstormError, 'id' | 'timestamp'>) => void;
  resolveError: (errorId: string) => void;
  clearErrors: () => void;
  
  // Utility functions
  generateId: () => string;
  exportSession: (sessionId: string, format: string) => Promise<void>;
  importSession: (data: any) => Promise<string>;
}

export const useBrainstormStore = create<BrainstormStore>()(
  persist(
    immer((set, get) => ({
      // Initial state
      sessions: {},
      currentSessionId: null,
      ideas: {},
      clusters: {},
      memories: {},
      templates: {},
      personas: DEFAULT_PERSONAS,
      relationships: {},
      
      // UI state
      currentView: ViewType.CHAT,
      currentPersona: DEFAULT_PERSONAS[0],
      isLoading: false,
      errors: [],
      
      // Settings
      settings: DEFAULT_SETTINGS,
      
      // Export and integration
      exportFormats: [],
      integrations: {},
      
      // Session management
      createSession: (title?: string, template?: TemplateType) => {
        const id = get().generateId();
        const now = new Date().toISOString();
        
        set((state) => {
          state.sessions[id] = {
            id,
            title: title || `Brainstorming Session ${Object.keys(state.sessions).length + 1}`,
            createdAt: now,
            updatedAt: now,
            messages: [],
            ideas: [],
            tags: [],
            template,
            metadata: {
              totalIdeas: 0,
              totalClusters: 0,
              lastActivity: now,
              template,
              personas: [state.currentPersona.id],
              exportHistory: []
            }
          };
          state.currentSessionId = id;
        });
        
        return id;
      },
      
      updateSession: (sessionId: string, updates: Partial<BrainstormSession>) => {
        set((state) => {
          if (state.sessions[sessionId]) {
            Object.assign(state.sessions[sessionId], {
              ...updates,
              updatedAt: new Date().toISOString()
            });
          }
        });
      },
      
      deleteSession: (sessionId: string) => {
        set((state) => {
          // Remove session
          // Get session before deletion
          const session = state.sessions[sessionId];
          const messageIds = session ? session.messages.map((m: ChatMessage) => m.id) : [];
          
          // Delete the session
          delete state.sessions[sessionId];
          
          // Remove associated ideas
          Object.keys(state.ideas).forEach(ideaId => {
            const idea = state.ideas[ideaId];
            if (idea.messageId && messageIds.includes(idea.messageId)) {
              delete state.ideas[ideaId];
            }
          });
          
          // Update current session if deleted
          if (state.currentSessionId === sessionId) {
            const remainingSessions = Object.keys(state.sessions);
            state.currentSessionId = remainingSessions.length > 0 ? remainingSessions[0] : null;
          }
        });
      },
      
      setCurrentSession: (sessionId: string | null) => {
        set((state) => {
          state.currentSessionId = sessionId;
        });
      },
      
      getCurrentSession: () => {
        const state = get();
        return state.currentSessionId ? state.sessions[state.currentSessionId] || null : null;
      },
      
      // Message management
      addMessage: (sessionId: string, message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
        const id = get().generateId();
        const timestamp = new Date().toISOString();
        
        set((state) => {
          if (state.sessions[sessionId]) {
            const newMessage: ChatMessage = {
              ...message,
              id,
              timestamp
            };
            state.sessions[sessionId].messages.push(newMessage);
            state.sessions[sessionId].updatedAt = timestamp;
            state.sessions[sessionId].metadata.lastActivity = timestamp;
          }
        });
        
        return id;
      },
      
      updateMessage: (sessionId: string, messageId: string, updates: Partial<ChatMessage>) => {
        set((state) => {
          const session = state.sessions[sessionId];
          if (session) {
            const messageIndex = session.messages.findIndex((m: ChatMessage) => m.id === messageId);
            if (messageIndex !== -1) {
              Object.assign(session.messages[messageIndex], updates);
              session.updatedAt = new Date().toISOString();
            }
          }
        });
      },
      
      // Idea management
      addIdea: (sessionId: string, content: string, options?: Partial<Omit<Idea, 'id' | 'createdAt' | 'updatedAt' | 'content' | 'messageId'>>) => {
        const id = get().generateId();
        const now = new Date().toISOString();
        const newIdea: Idea = {
          id,
          content,
          messageId: '', // Will be linked to a message later
          status: options?.status || IdeaStatus.TO_EXPLORE,
          tags: options?.tags || [],
          cluster: options?.cluster,
          priority: options?.priority,
          connections: options?.connections || [],
          position: options?.position,
          impact: options?.impact,
          effort: options?.effort,
          metadata: options?.metadata,
          createdAt: now,
          updatedAt: now
        };
        
        set((state) => {
          state.ideas[id] = newIdea;
          
          // Update session metadata
          const session = state.sessions[sessionId];
          if (session) {
            session.metadata.totalIdeas++;
            session.updatedAt = now;
          }
        });
        
        return newIdea;
      },
      
      addIdeaFull: (idea: Omit<Idea, 'id' | 'createdAt' | 'updatedAt'>) => {
        const id = get().generateId();
        const now = new Date().toISOString();
        
        set((state) => {
          state.ideas[id] = {
            ...idea,
            id,
            createdAt: now,
            updatedAt: now
          };
          
          // Update session metadata
          const sessions = Object.values(state.sessions) as BrainstormSession[];
          const session = sessions.find((s) => 
            s.messages.some((m: ChatMessage) => m.id === idea.messageId)
          );
          if (session) {
            session.metadata.totalIdeas++;
            session.updatedAt = now;
          }
        });
        
        return id;
      },
      
      updateIdea: (ideaId: string, updates: Partial<Idea>) => {
        set((state) => {
          if (state.ideas[ideaId]) {
            Object.assign(state.ideas[ideaId], {
              ...updates,
              updatedAt: new Date().toISOString()
            });
          }
        });
      },
      
      deleteIdea: (ideaId: string) => {
        set((state) => {
          const idea = state.ideas[ideaId];
          if (idea) {
            // Remove from cluster if assigned
            if (idea.cluster) {
              const cluster = state.clusters[idea.cluster];
              if (cluster) {
                cluster.ideaIds = cluster.ideaIds.filter((id: string) => id !== ideaId);
                if (cluster.ideaIds.length === 0) {
                  delete state.clusters[idea.cluster];
                }
              }
            }
            
            // Remove idea
            delete state.ideas[ideaId];
          }
        });
      },
      
      getIdeasBySession: (sessionId: string) => {
        const state = get();
        const session = state.sessions[sessionId];
        if (!session) return [];
        
        const messageIds = new Set(session.messages.map(m => m.id));
        return Object.values(state.ideas).filter(idea => messageIds.has(idea.messageId));
      },
      
      getIdeasByStatus: (status: IdeaStatus) => {
        const state = get();
        return Object.values(state.ideas).filter(idea => idea.status === status);
      },
      
      getIdeaById: (ideaId: string) => {
        const state = get();
        return state.ideas[ideaId];
      },
      
      // Relationship management
      addRelationship: (sourceId: string, targetId: string, type: IdeaRelationship['type'], label?: string) => {
        const id = get().generateId();
        const now = new Date().toISOString();
        
        set((state) => {
          state.relationships[id] = {
            id,
            sourceId,
            targetId,
            type,
            label,
            createdAt: now
          };
          
          // Update ideas connections
          if (state.ideas[sourceId] && !state.ideas[sourceId].connections.includes(targetId)) {
            state.ideas[sourceId].connections.push(targetId);
          }
          if (state.ideas[targetId] && !state.ideas[targetId].connections.includes(sourceId)) {
            state.ideas[targetId].connections.push(sourceId);
          }
        });
        
        return id;
      },
      
      removeRelationship: (relationshipId: string) => {
        set((state) => {
          const relationship = state.relationships[relationshipId];
          if (relationship) {
            // Remove from ideas connections
            if (state.ideas[relationship.sourceId]) {
              state.ideas[relationship.sourceId].connections = state.ideas[relationship.sourceId].connections.filter(
                id => id !== relationship.targetId
              );
            }
            if (state.ideas[relationship.targetId]) {
              state.ideas[relationship.targetId].connections = state.ideas[relationship.targetId].connections.filter(
                id => id !== relationship.sourceId
              );
            }
            
            delete state.relationships[relationshipId];
          }
        });
      },
      
      getIdeaRelationships: (sessionId: string) => {
        const state = get();
        const sessionIdeas = state.getIdeasBySession(sessionId);
        const ideaIds = new Set(sessionIdeas.map(idea => idea.id));
        
        return Object.values(state.relationships).filter(rel => 
          ideaIds.has(rel.sourceId) || ideaIds.has(rel.targetId)
        );
      },
      
      getRelationshipsForIdea: (ideaId: string) => {
        const state = get();
        return Object.values(state.relationships).filter(rel => 
          rel.sourceId === ideaId || rel.targetId === ideaId
        );
      },
      
      // Cluster management
      createCluster: (name: string, theme: string, ideaIds: string[]) => {
        const id = get().generateId();
        const now = new Date().toISOString();
        
        set((state) => {
          state.clusters[id] = {
            id,
            name,
            theme,
            ideaIds: [...ideaIds],
            color: `#${Math.floor(Math.random()*16777215).toString(16)}`, // Random color
            createdAt: now,
            updatedAt: now
          };
          
          // Update ideas to reference cluster
          ideaIds.forEach(ideaId => {
            if (state.ideas[ideaId]) {
              state.ideas[ideaId].cluster = id;
            }
          });
        });
        
        return id;
      },
      
      updateCluster: (clusterId: string, updates: Partial<IdeaCluster>) => {
        set((state) => {
          if (state.clusters[clusterId]) {
            Object.assign(state.clusters[clusterId], {
              ...updates,
              updatedAt: new Date().toISOString()
            });
          }
        });
      },
      
      deleteCluster: (clusterId: string) => {
        set((state) => {
          const cluster = state.clusters[clusterId];
          if (cluster) {
            // Remove cluster reference from ideas
            cluster.ideaIds.forEach((ideaId: string) => {
              if (state.ideas[ideaId]) {
                delete state.ideas[ideaId].cluster;
              }
            });
            
            delete state.clusters[clusterId];
          }
        });
      },
      
      addIdeaToCluster: (ideaId: string, clusterId: string) => {
        set((state) => {
          const cluster = state.clusters[clusterId];
          const idea = state.ideas[ideaId];
          
          if (cluster && idea) {
            // Remove from previous cluster if any
            if (idea.cluster && idea.cluster !== clusterId) {
              const oldCluster = state.clusters[idea.cluster];
              if (oldCluster) {
                oldCluster.ideaIds = oldCluster.ideaIds.filter((id: string) => id !== ideaId);
              }
            }
            
            // Add to new cluster
            if (!cluster.ideaIds.includes(ideaId)) {
              cluster.ideaIds.push(ideaId);
            }
            idea.cluster = clusterId;
            
            cluster.updatedAt = new Date().toISOString();
          }
        });
      },
      
      removeIdeaFromCluster: (ideaId: string) => {
        set((state) => {
          const idea = state.ideas[ideaId];
          if (idea && idea.cluster) {
            const cluster = state.clusters[idea.cluster];
            if (cluster) {
              cluster.ideaIds = cluster.ideaIds.filter((id: string) => id !== ideaId);
              cluster.updatedAt = new Date().toISOString();
              
              // Delete cluster if empty
              if (cluster.ideaIds.length === 0) {
                delete state.clusters[idea.cluster];
              }
            }
            delete idea.cluster;
          }
        });
      },
      
      // Memory management
      saveMemory: (memory: Omit<PersistentMemory, 'id' | 'createdAt'>) => {
        const id = get().generateId();
        const now = new Date().toISOString();
        
        set((state) => {
          state.memories[id] = {
            ...memory,
            id,
            createdAt: now
          };
          
          // Enforce max memories limit
          const memories = Object.values(state.memories) as PersistentMemory[];
          if (memories.length > state.settings.maxMemories) {
            // Remove oldest memories
            const sorted = memories.sort((a, b) => 
              new Date(a.lastUsed || a.createdAt).getTime() - new Date(b.lastUsed || b.createdAt).getTime()
            );
            const toRemove = sorted.slice(0, memories.length - state.settings.maxMemories);
            toRemove.forEach(mem => delete state.memories[mem.id]);
          }
        });
        
        return id;
      },
      
      updateMemory: (memoryId: string, updates: Partial<PersistentMemory>) => {
        set((state) => {
          if (state.memories[memoryId]) {
            Object.assign(state.memories[memoryId], updates);
          }
        });
      },
      
      deleteMemory: (memoryId: string) => {
        set((state) => {
          delete state.memories[memoryId];
        });
      },
      
      getRelevantMemories: (sessionId: string, query?: string) => {
        const state = get();
        const memories = Object.values(state.memories);
        
        // Simple relevance scoring - in production, use more sophisticated matching
        return memories
          .filter(memory => {
            if (memory.sessionId === sessionId) return true;
            if (query) {
              const searchText = query.toLowerCase();
              return memory.content.toLowerCase().includes(searchText) ||
                     memory.context.toLowerCase().includes(searchText) ||
                     memory.tags.some(tag => tag.toLowerCase().includes(searchText));
            }
            return false;
          })
          .sort((a, b) => b.importance - a.importance)
          .slice(0, 10); // Return top 10 relevant memories
      },
      
      // Template management
      addTemplate: (template: Omit<BrainstormTemplate, 'id' | 'createdAt' | 'updatedAt'>) => {
        const id = get().generateId();
        const now = new Date().toISOString();
        
        set((state) => {
          state.templates[id] = {
            ...template,
            id,
            createdAt: now,
            updatedAt: now
          };
        });
        
        return id;
      },
      
      updateTemplate: (templateId: string, updates: Partial<BrainstormTemplate>) => {
        set((state) => {
          if (state.templates[templateId]) {
            Object.assign(state.templates[templateId], {
              ...updates,
              updatedAt: new Date().toISOString()
            });
          }
        });
      },
      
      deleteTemplate: (templateId: string) => {
        set((state) => {
          delete state.templates[templateId];
        });
      },
      
      // Persona management
      setCurrentPersona: (persona: AIPersona) => {
        set((state) => {
          // Update all personas to inactive
          state.personas.forEach((p: AIPersona) => p.isActive = false);
          
          // Find and activate the selected persona
          const targetPersona = state.personas.find((p: AIPersona) => p.id === persona.id);
          if (targetPersona) {
            targetPersona.isActive = true;
            state.currentPersona = targetPersona;
          }
        });
      },
      
      updatePersona: (personaId: string, updates: Partial<AIPersona>) => {
        set((state) => {
          const persona = state.personas.find((p: AIPersona) => p.id === personaId);
          if (persona) {
            Object.assign(persona, updates);
            if (state.currentPersona.id === personaId) {
              state.currentPersona = persona;
            }
          }
        });
      },
      
      // View management
      setCurrentView: (view: ViewType) => {
        set((state) => {
          state.currentView = view;
        });
      },
      
      // Search functionality
      searchSessions: (query: SearchQuery) => {
        const state = get();
        const results: SearchResult[] = [];
        
        // Simple search implementation - in production, use more sophisticated indexing
        Object.values(state.sessions).forEach(session => {
          // Search messages
          session.messages.forEach(message => {
            if (query.text && message.content.toLowerCase().includes(query.text.toLowerCase())) {
              results.push({
                id: `${session.id}-${message.id}`,
                type: 'message',
                title: `Message in ${session.title}`,
                content: message.content,
                context: session.title,
                relevance: 0.8,
                highlights: [query.text],
                sessionId: session.id,
                timestamp: message.timestamp
              });
            }
          });
          
          // Search ideas
          const sessionIdeas = get().getIdeasBySession(session.id);
          sessionIdeas.forEach(idea => {
            if (query.text && idea.content.toLowerCase().includes(query.text.toLowerCase())) {
              results.push({
                id: idea.id,
                type: 'idea',
                title: `Idea: ${idea.content.substring(0, 50)}...`,
                content: idea.content,
                context: session.title,
                relevance: 0.9,
                highlights: [query.text],
                sessionId: session.id,
                timestamp: idea.createdAt
              });
            }
          });
        });
        
        return results.sort((a, b) => b.relevance - a.relevance);
      },
      
      // Settings management
      updateSettings: (updates: Partial<BrainstormSettings>) => {
        set((state) => {
          Object.assign(state.settings, updates);
        });
      },
      
      // Error handling
      addError: (error: Omit<BrainstormError, 'id' | 'timestamp'>) => {
        const id = get().generateId();
        const timestamp = new Date().toISOString();
        
        set((state) => {
          state.errors.push({
            ...error,
            id,
            timestamp,
            resolved: false
          });
        });
      },
      
      resolveError: (errorId: string) => {
        set((state) => {
          const error = state.errors.find((e: BrainstormError) => e.id === errorId);
          if (error) {
            error.resolved = true;
          }
        });
      },
      
      clearErrors: () => {
        set((state) => {
          state.errors = [];
        });
      },
      
      // Utility functions
      generateId: () => {
        return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      },
      
      exportSession: async (sessionId: string, format: string) => {
        try {
          const state = get();
          const session = state.sessions[sessionId];
          if (!session) {
            throw new Error(`Session ${sessionId} not found`);
          }

          // Get session data with ideas and clusters
          const sessionIdeas = state.getIdeasBySession(sessionId);
          const sessionClusters = Object.values(state.clusters).filter(cluster => 
            cluster.ideaIds.some(ideaId => sessionIdeas.some(idea => idea.id === ideaId))
          );
          const sessionRelationships = state.getIdeaRelationships(sessionId);

          const exportData = {
            session,
            ideas: sessionIdeas,
            clusters: sessionClusters,
            relationships: sessionRelationships,
            exportedAt: new Date().toISOString(),
            exportFormat: format,
            version: '1.0'
          };

          let content: string;
          let extension: string;
          let mimeType: string;

          switch (format.toLowerCase()) {
            case 'json':
              content = JSON.stringify(exportData, null, 2);
              extension = 'json';
              mimeType = 'application/json';
              break;
            
            case 'markdown':
              content = await generateMarkdownExport(exportData);
              extension = 'md';
              mimeType = 'text/markdown';
              break;
            
            case 'csv':
              content = await generateCSVExport(exportData);
              extension = 'csv';
              mimeType = 'text/csv';
              break;
            
            default:
              throw new Error(`Unsupported export format: ${format}`);
          }

          // Use Tauri dialog to save file
          const { save } = await import('@tauri-apps/plugin-dialog');
          const filePath = await save({
            defaultPath: `${session.title.replace(/[^a-zA-Z0-9]/g, '_')}.${extension}`,
            filters: [{
              name: `${format.toUpperCase()} files`,
              extensions: [extension]
            }]
          });

          if (filePath) {
            // Write file using Tauri filesystem
            const { writeTextFile } = await import('@tauri-apps/plugin-fs');
            await writeTextFile(filePath, content);

            // Update session metadata
            state.updateSession(sessionId, {
              metadata: {
                ...session.metadata,
                exportHistory: [
                  ...(session.metadata.exportHistory || []),
                  {
                    format,
                    exportedAt: new Date().toISOString(),
                    filePath
                  }
                ]
              }
            });

            // Show success notification if notifications enabled
            if (state.settings.notifications.exports) {
              // Could integrate with a notification system here
              console.log(`Session exported successfully to ${filePath}`);
            }
          }
        } catch (error) {
          console.error('Export failed:', error);
          const state = get();
          state.addError({
            type: 'export',
            message: `Failed to export session: ${error instanceof Error ? error.message : 'Unknown error'}`,
            context: { sessionId, format }
          });
          throw error;
        }
      },
      
      importSession: async (data: any): Promise<string> => {
        try {
          const state = get();
          let sessionData: any;

          // Handle different import data types
          if (typeof data === 'string') {
            // Try to parse as JSON
            try {
              sessionData = JSON.parse(data);
            } catch {
              throw new Error('Invalid JSON data');
            }
          } else {
            sessionData = data;
          }

          // Validate import data structure
          if (!sessionData.session && !sessionData.title) {
            throw new Error('Invalid session data: missing session information');
          }

          // Generate new IDs for imported content
          const sessionId = state.generateId();
          const idMapping = new Map<string, string>();

          // Import session
          const importedSession: BrainstormSession = {
            id: sessionId,
            title: sessionData.session?.title || sessionData.title || 'Imported Session',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            messages: [],
            ideas: [],
            tags: sessionData.session?.tags || [],
            template: sessionData.session?.template,
            metadata: {
              totalIdeas: sessionData.ideas?.length || 0,
              totalClusters: sessionData.clusters?.length || 0,
              lastActivity: new Date().toISOString(),
              template: sessionData.session?.template,
              personas: sessionData.session?.metadata?.personas || [state.currentPersona.id],
              exportHistory: [],
              importedFrom: {
                originalId: sessionData.session?.id,
                importedAt: new Date().toISOString(),
                source: sessionData.exportFormat || 'unknown'
              }
            }
          };

          set((draft) => {
            draft.sessions[sessionId] = importedSession;
          });

          // Import ideas if available
          if (sessionData.ideas && Array.isArray(sessionData.ideas)) {
            for (const idea of sessionData.ideas) {
              const newIdeaId = state.generateId();
              idMapping.set(idea.id, newIdeaId);

              // Create message for the idea if not exists
              const messageId = state.addMessage(sessionId, {
                role: 'user',
                content: idea.content,
                metadata: { importedIdea: true }
              });

              set((draft) => {
                draft.ideas[newIdeaId] = {
                  ...idea,
                  id: newIdeaId,
                  messageId,
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                  connections: [], // Will be updated when relationships are imported
                  cluster: undefined // Will be updated when clusters are imported
                };
              });
            }
          }

          // Import clusters if available
          if (sessionData.clusters && Array.isArray(sessionData.clusters)) {
            for (const cluster of sessionData.clusters) {
              const newClusterId = state.generateId();
              const mappedIdeaIds = cluster.ideaIds
                .map((id: string) => idMapping.get(id))
                .filter((id: string | undefined) => id !== undefined);

              if (mappedIdeaIds.length > 0) {
                set((draft) => {
                  draft.clusters[newClusterId] = {
                    ...cluster,
                    id: newClusterId,
                    ideaIds: mappedIdeaIds,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                  };

                  // Update ideas to reference the cluster
                  mappedIdeaIds.forEach((ideaId: string) => {
                    if (draft.ideas[ideaId]) {
                      draft.ideas[ideaId].cluster = newClusterId;
                    }
                  });
                });
              }
            }
          }

          // Import relationships if available
          if (sessionData.relationships && Array.isArray(sessionData.relationships)) {
            for (const relationship of sessionData.relationships) {
              const newSourceId = idMapping.get(relationship.sourceId);
              const newTargetId = idMapping.get(relationship.targetId);

              if (newSourceId && newTargetId) {
                state.addRelationship(newSourceId, newTargetId, relationship.type, relationship.label);
              }
            }
          }

          // Set as current session
          state.setCurrentSession(sessionId);

          // Show success notification if notifications enabled
          if (state.settings.notifications.exports) {
            console.log(`Session imported successfully with ID: ${sessionId}`);
          }

          return sessionId;
        } catch (error) {
          console.error('Import failed:', error);
          const state = get();
          state.addError({
            type: 'import',
            message: `Failed to import session: ${error instanceof Error ? error.message : 'Unknown error'}`,
            context: { data }
          });
          throw error;
        }
      }
    })),
    {
      name: 'brainstorm-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        sessions: state.sessions,
        ideas: state.ideas,
        clusters: state.clusters,
        memories: state.memories,
        templates: state.templates,
        personas: state.personas,
        settings: state.settings,
        integrations: state.integrations
      })
    }
  )
);

// Export format generators
async function generateMarkdownExport(exportData: any): Promise<string> {
  const { session, ideas, clusters, relationships } = exportData;
  
  let markdown = `# ${session.title}\n\n`;
  markdown += `**Created:** ${new Date(session.createdAt).toLocaleDateString()}\n`;
  markdown += `**Last Updated:** ${new Date(session.updatedAt).toLocaleDateString()}\n`;
  markdown += `**Total Ideas:** ${ideas.length}\n`;
  markdown += `**Total Clusters:** ${clusters.length}\n\n`;

  if (session.tags && session.tags.length > 0) {
    markdown += `**Tags:** ${session.tags.join(', ')}\n\n`;
  }

  // Export chat messages
  if (session.messages && session.messages.length > 0) {
    markdown += `## Conversation\n\n`;
    session.messages.forEach((message: any) => {
      const timestamp = new Date(message.timestamp).toLocaleString();
      markdown += `### ${message.role === 'user' ? 'User' : 'Assistant'} (${timestamp})\n\n`;
      markdown += `${message.content}\n\n`;
    });
  }

  // Export ideas
  if (ideas.length > 0) {
    markdown += `## Ideas\n\n`;
    ideas.forEach((idea: any, index: number) => {
      markdown += `### ${index + 1}. ${idea.content}\n\n`;
      if (idea.status) {
        markdown += `**Status:** ${idea.status}\n`;
      }
      if (idea.priority) {
        markdown += `**Priority:** ${idea.priority}\n`;
      }
      if (idea.tags && idea.tags.length > 0) {
        markdown += `**Tags:** ${idea.tags.join(', ')}\n`;
      }
      if (idea.cluster) {
        const cluster = clusters.find((c: any) => c.id === idea.cluster);
        if (cluster) {
          markdown += `**Cluster:** ${cluster.name}\n`;
        }
      }
      markdown += `**Created:** ${new Date(idea.createdAt).toLocaleDateString()}\n\n`;
    });
  }

  // Export clusters
  if (clusters.length > 0) {
    markdown += `## Clusters\n\n`;
    clusters.forEach((cluster: any, index: number) => {
      markdown += `### ${index + 1}. ${cluster.name}\n\n`;
      markdown += `**Theme:** ${cluster.theme}\n`;
      markdown += `**Ideas Count:** ${cluster.ideaIds.length}\n`;
      markdown += `**Created:** ${new Date(cluster.createdAt).toLocaleDateString()}\n\n`;
      
      // List ideas in cluster
      if (cluster.ideaIds.length > 0) {
        markdown += `**Ideas in this cluster:**\n`;
        cluster.ideaIds.forEach((ideaId: string) => {
          const idea = ideas.find((i: any) => i.id === ideaId);
          if (idea) {
            markdown += `- ${idea.content}\n`;
          }
        });
        markdown += '\n';
      }
    });
  }

  // Export relationships
  if (relationships.length > 0) {
    markdown += `## Idea Relationships\n\n`;
    relationships.forEach((rel: any) => {
      const sourceIdea = ideas.find((i: any) => i.id === rel.sourceId);
      const targetIdea = ideas.find((i: any) => i.id === rel.targetId);
      if (sourceIdea && targetIdea) {
        markdown += `- **${sourceIdea.content}** ${rel.type} **${targetIdea.content}**`;
        if (rel.label) {
          markdown += ` (${rel.label})`;
        }
        markdown += '\n';
      }
    });
    markdown += '\n';
  }

  markdown += `---\n*Exported on ${new Date().toLocaleDateString()} from Claudia Brainstorming*\n`;
  
  return markdown;
}

async function generateCSVExport(exportData: any): Promise<string> {
  const { ideas, clusters } = exportData;
  
  let csv = 'ID,Content,Status,Priority,Tags,Cluster,CreatedAt,UpdatedAt\n';
  
  ideas.forEach((idea: any) => {
    const cluster = clusters.find((c: any) => c.id === idea.cluster);
    const clusterName = cluster ? cluster.name : '';
    const tags = idea.tags ? idea.tags.join(';') : '';
    
    // Escape CSV values
    const escapeCsvValue = (value: string) => {
      if (value.includes(',') || value.includes('"') || value.includes('\n')) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    };
    
    csv += [
      idea.id,
      escapeCsvValue(idea.content),
      idea.status || '',
      idea.priority || '',
      escapeCsvValue(tags),
      escapeCsvValue(clusterName),
      idea.createdAt,
      idea.updatedAt
    ].join(',') + '\n';
  });
  
  return csv;
}

// Add file system-based persistence functions
export const brainstormPersistence = {
  async saveToFile(filePath: string, data: any): Promise<void> {
    try {
      const { writeTextFile } = await import('@tauri-apps/plugin-fs');
      await writeTextFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Failed to save to file:', error);
      throw error;
    }
  },

  async loadFromFile(filePath: string): Promise<any> {
    try {
      const { readTextFile } = await import('@tauri-apps/plugin-fs');
      const content = await readTextFile(filePath);
      return JSON.parse(content);
    } catch (error) {
      console.error('Failed to load from file:', error);
      throw error;
    }
  },

  async backupAllSessions(): Promise<string> {
    try {
      const store = useBrainstormStore.getState();
      const backupData = {
        sessions: store.sessions,
        ideas: store.ideas,
        clusters: store.clusters,
        memories: store.memories,
        templates: store.templates,
        relationships: store.relationships,
        settings: store.settings,
        backupDate: new Date().toISOString(),
        version: '1.0'
      };

      const { save } = await import('@tauri-apps/plugin-dialog');
      const filePath = await save({
        defaultPath: `claudia-brainstorm-backup-${new Date().toISOString().split('T')[0]}.json`,
        filters: [{
          name: 'JSON files',
          extensions: ['json']
        }]
      });

      if (filePath) {
        await brainstormPersistence.saveToFile(filePath, backupData);
        return filePath;
      }
      
      throw new Error('No file path selected');
    } catch (error) {
      console.error('Backup failed:', error);
      throw error;
    }
  },

  async restoreFromBackup(filePath?: string): Promise<void> {
    try {
      let selectedPath = filePath;
      
      if (!selectedPath) {
        const { open } = await import('@tauri-apps/plugin-dialog');
        selectedPath = await open({
          filters: [{
            name: 'JSON files',
            extensions: ['json']
          }]
        });
      }

      if (!selectedPath || Array.isArray(selectedPath)) {
        throw new Error('No file selected');
      }

      const backupData = await brainstormPersistence.loadFromFile(selectedPath);
      
      // Validate backup data
      if (!backupData.sessions || !backupData.version) {
        throw new Error('Invalid backup file format');
      }

      // Restore data to store
      const store = useBrainstormStore.getState();
      useBrainstormStore.setState({
        sessions: backupData.sessions || {},
        ideas: backupData.ideas || {},
        clusters: backupData.clusters || {},
        memories: backupData.memories || {},
        templates: backupData.templates || {},
        relationships: backupData.relationships || {},
        settings: { ...store.settings, ...backupData.settings }
      });

      console.log('Backup restored successfully');
    } catch (error) {
      console.error('Restore failed:', error);
      throw error;
    }
  }
};