/**
 * Debugger Panel Component
 * Provides comprehensive debugging support with breakpoints and variable inspection
 */

import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Play,
  Pause,
  Square,
  StepOver,
  StepInto,
  StepOut,
  SkipForward,
  Bug,
  Circle,
  AlertCircle,
  Eye,
  EyeOff,
  RefreshCw,
  ChevronRight,
  ChevronDown,
  Terminal,
  FileText,
  Settings,
  Zap,
  Target,
  Activity,
  Code,
  Variable,
  List,
  ArrowRight
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";
import { useToast } from "@/contexts/ToastContext";

interface Breakpoint {
  id: string;
  file: string;
  line: number;
  enabled: boolean;
  condition?: string;
  hitCount: number;
  logMessage?: string;
}

interface StackFrame {
  id: string;
  name: string;
  file: string;
  line: number;
  column: number;
  source?: string;
}

interface Variable {
  name: string;
  value: any;
  type: string;
  expandable: boolean;
  scope: 'local' | 'global' | 'closure';
  children?: Variable[];
}

interface DebugSession {
  id: string;
  name: string;
  status: 'running' | 'paused' | 'stopped' | 'starting';
  type: 'node' | 'python' | 'browser' | 'custom';
  config: any;
}

interface WatchExpression {
  id: string;
  expression: string;
  value: any;
  error?: string;
}

interface DebuggerPanelProps {
  className?: string;
  onBreakpointSet?: (file: string, line: number) => void;
  onPromptSend?: (prompt: string) => void;
  currentFile?: string;
  currentLine?: number;
}

export const DebuggerPanel: React.FC<DebuggerPanelProps> = ({
  className,
  onBreakpointSet,
  onPromptSend,
  currentFile,
  currentLine
}) => {
  const [debugSession, setDebugSession] = useState<DebugSession | null>(null);
  const [breakpoints, setBreakpoints] = useState<Breakpoint[]>([]);
  const [callStack, setCallStack] = useState<StackFrame[]>([]);
  const [variables, setVariables] = useState<Variable[]>([]);
  const [watchExpressions, setWatchExpressions] = useState<WatchExpression[]>([]);
  const [selectedFrame, setSelectedFrame] = useState<string | null>(null);
  const [expandedVariables, setExpandedVariables] = useState<Set<string>>(new Set());
  const [newWatchExpression, setNewWatchExpression] = useState("");
  const [selectedTab, setSelectedTab] = useState<"breakpoints" | "variables" | "watch" | "callstack" | "console">("breakpoints");
  const [consoleOutput, setConsoleOutput] = useState<Array<{type: 'log' | 'error' | 'warn', message: string, timestamp: Date}>>([]);
  const [debugSettings, setDebugSettings] = useState({
    pauseOnExceptions: false,
    pauseOnCaughtExceptions: false,
    showInternalFrames: false,
    enableStepFiltering: true
  });

  const { addToast } = useToast();

  // Mock debugging data
  const mockBreakpoints: Breakpoint[] = [
    {
      id: "bp1",
      file: "src/components/ClaudeCodeSession.tsx",
      line: 145,
      enabled: true,
      hitCount: 3,
      condition: "currentSession !== null"
    },
    {
      id: "bp2", 
      file: "src/lib/api.ts",
      line: 67,
      enabled: false,
      hitCount: 0,
      logMessage: "API call started: {endpoint}"
    },
    {
      id: "bp3",
      file: "src/hooks/useTabState.ts", 
      line: 23,
      enabled: true,
      hitCount: 1
    }
  ];

  const mockCallStack: StackFrame[] = [
    {
      id: "frame1",
      name: "handleSendPrompt",
      file: "src/components/ClaudeCodeSession.tsx",
      line: 174,
      column: 12
    },
    {
      id: "frame2", 
      name: "onSend",
      file: "src/components/FloatingPromptInput.tsx",
      line: 89,
      column: 5
    },
    {
      id: "frame3",
      name: "onClick",
      file: "src/components/ui/button.tsx",
      line: 45,
      column: 8
    }
  ];

  const mockVariables: Variable[] = [
    {
      name: "currentSession",
      value: {id: "sess_123", name: "Project Session"},
      type: "Session",
      expandable: true,
      scope: "local",
      children: [
        {name: "id", value: "sess_123", type: "string", expandable: false, scope: "local"},
        {name: "name", value: "Project Session", type: "string", expandable: false, scope: "local"},
        {name: "project_path", value: "/Users/<USER>/project", type: "string", expandable: false, scope: "local"}
      ]
    },
    {
      name: "prompt",
      value: "Explain this code",
      type: "string", 
      expandable: false,
      scope: "local"
    },
    {
      name: "selectedModel",
      value: "claude-3-5-sonnet-20241022",
      type: "ModelType",
      expandable: false,
      scope: "local"
    },
    {
      name: "loading",
      value: false,
      type: "boolean",
      expandable: false,
      scope: "local"
    },
    {
      name: "settings",
      value: {temperature: 0.7, maxTokens: 4096},
      type: "object",
      expandable: true,
      scope: "local",
      children: [
        {name: "temperature", value: 0.7, type: "number", expandable: false, scope: "local"},
        {name: "maxTokens", value: 4096, type: "number", expandable: false, scope: "local"}
      ]
    }
  ];

  const mockWatchExpressions: WatchExpression[] = [
    {
      id: "watch1",
      expression: "currentSession?.id",
      value: "sess_123"
    },
    {
      id: "watch2", 
      expression: "messages.length",
      value: 5
    },
    {
      id: "watch3",
      expression: "isStreaming && loading",
      value: false
    }
  ];

  // Initialize mock data
  useEffect(() => {
    setBreakpoints(mockBreakpoints);
    setCallStack(mockCallStack);
    setVariables(mockVariables);
    setWatchExpressions(mockWatchExpressions);
    setSelectedFrame("frame1");
    
    // Mock some console output
    setConsoleOutput([
      {type: 'log', message: 'Debug session started', timestamp: new Date()},
      {type: 'log', message: 'Loading session data...', timestamp: new Date()},
      {type: 'warn', message: 'Session history not found, starting fresh', timestamp: new Date()}
    ]);
  }, []);

  // Debug session controls
  const startDebugSession = useCallback(async () => {
    try {
      setDebugSession({
        id: "debug_session_1",
        name: "Claude Code Debug",
        status: "starting",
        type: "node",
        config: {}
      });

      setTimeout(() => {
        setDebugSession(prev => prev ? {...prev, status: "running"} : null);
        addToast({
          title: "Debug session started",
          description: "Debugger is now attached and running",
          variant: "success"
        });
      }, 1000);
    } catch (err) {
      addToast({
        title: "Failed to start debug session",
        description: err instanceof Error ? err.message : "Unknown error",
        variant: "error"
      });
    }
  }, [addToast]);

  const pauseDebugSession = useCallback(async () => {
    if (debugSession) {
      setDebugSession({...debugSession, status: "paused"});
      addToast({
        title: "Debug session paused",
        description: "Execution has been paused",
        variant: "success"
      });
    }
  }, [debugSession, addToast]);

  const stopDebugSession = useCallback(async () => {
    setDebugSession(null);
    addToast({
      title: "Debug session stopped",
      description: "Debugger has been detached",
      variant: "success"
    });
  }, [addToast]);

  const stepOver = useCallback(async () => {
    addToast({
      title: "Step over",
      description: "Executing next line",
      variant: "success"
    });
  }, [addToast]);

  const stepInto = useCallback(async () => {
    addToast({
      title: "Step into",
      description: "Stepping into function call",
      variant: "success"
    });
  }, [addToast]);

  const stepOut = useCallback(async () => {
    addToast({
      title: "Step out", 
      description: "Stepping out of current function",
      variant: "success"
    });
  }, [addToast]);

  const continueExecution = useCallback(async () => {
    if (debugSession) {
      setDebugSession({...debugSession, status: "running"});
      addToast({
        title: "Continuing execution",
        description: "Debug session resumed",
        variant: "success"
      });
    }
  }, [debugSession, addToast]);

  // Breakpoint management
  const toggleBreakpoint = useCallback((id: string) => {
    setBreakpoints(prev => prev.map(bp => 
      bp.id === id ? {...bp, enabled: !bp.enabled} : bp
    ));
  }, []);

  const removeBreakpoint = useCallback((id: string) => {
    setBreakpoints(prev => prev.filter(bp => bp.id !== id));
  }, []);

  const addBreakpoint = useCallback((file: string, line: number) => {
    const newBreakpoint: Breakpoint = {
      id: `bp_${Date.now()}`,
      file,
      line,
      enabled: true,
      hitCount: 0
    };
    setBreakpoints(prev => [...prev, newBreakpoint]);
    onBreakpointSet?.(file, line);
  }, [onBreakpointSet]);

  // Variable inspection
  const toggleVariableExpansion = useCallback((name: string) => {
    setExpandedVariables(prev => {
      const newSet = new Set(prev);
      if (newSet.has(name)) {
        newSet.delete(name);
      } else {
        newSet.add(name);
      }
      return newSet;
    });
  }, []);

  // Watch expressions
  const addWatchExpression = useCallback(() => {
    if (!newWatchExpression.trim()) return;

    const newWatch: WatchExpression = {
      id: `watch_${Date.now()}`,
      expression: newWatchExpression,
      value: "Evaluating..."
    };
    
    setWatchExpressions(prev => [...prev, newWatch]);
    setNewWatchExpression("");

    // Mock evaluation
    setTimeout(() => {
      setWatchExpressions(prev => prev.map(w => 
        w.id === newWatch.id ? {...w, value: "undefined"} : w
      ));
    }, 500);
  }, [newWatchExpression]);

  const removeWatchExpression = useCallback((id: string) => {
    setWatchExpressions(prev => prev.filter(w => w.id !== id));
  }, []);

  // AI Integration
  const explainCurrentState = useCallback(() => {
    const currentFrame = callStack.find(f => f.id === selectedFrame);
    const prompt = `Explain the current debugging state: 
    - Current function: ${currentFrame?.name || 'Unknown'}
    - File: ${currentFrame?.file || 'Unknown'}
    - Line: ${currentFrame?.line || 'Unknown'}
    - Variables in scope: ${variables.map(v => `${v.name}: ${v.value}`).join(', ')}
    - Active breakpoints: ${breakpoints.filter(bp => bp.enabled).length}`;
    
    onPromptSend?.(prompt);
  }, [callStack, selectedFrame, variables, breakpoints, onPromptSend]);

  const suggestDebugStrategy = useCallback(() => {
    const prompt = `I'm debugging this code. Based on the current state with ${breakpoints.length} breakpoints and ${variables.length} variables in scope, suggest debugging strategies and potential issues to investigate.`;
    onPromptSend?.(prompt);
  }, [breakpoints.length, variables.length, onPromptSend]);

  const renderVariable = (variable: Variable, depth = 0) => (
    <div key={variable.name} style={{ marginLeft: depth * 16 }}>
      <div className="flex items-center gap-2 p-1 hover:bg-muted/50 rounded">
        {variable.expandable && (
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0"
            onClick={() => toggleVariableExpansion(variable.name)}
          >
            {expandedVariables.has(variable.name) ? 
              <ChevronDown className="h-3 w-3" /> : 
              <ChevronRight className="h-3 w-3" />
            }
          </Button>
        )}
        <Variable className="h-3 w-3 text-muted-foreground" />
        <span className="font-mono text-sm">{variable.name}</span>
        <span className="text-xs text-muted-foreground">({variable.type})</span>
        <span className="font-mono text-xs ml-auto">
          {typeof variable.value === 'object' && variable.value !== null
            ? JSON.stringify(variable.value).substring(0, 50) + "..."
            : String(variable.value)
          }
        </span>
      </div>
      {variable.expandable && expandedVariables.has(variable.name) && variable.children && (
        <div className="ml-4">
          {variable.children.map(child => renderVariable(child, depth + 1))}
        </div>
      )}
    </div>
  );

  return (
    <div className={cn("flex flex-col h-full border-l", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b bg-muted/30">
        <div className="flex items-center gap-2">
          <Bug className="h-4 w-4 text-primary" />
          <span className="font-medium">Debugger</span>
          {debugSession && (
            <Badge 
              variant={debugSession.status === "running" ? "default" : "secondary"}
              className="text-xs"
            >
              {debugSession.status}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={explainCurrentState}
            disabled={!debugSession}
            title="Explain current state with AI"
          >
            <Eye className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={suggestDebugStrategy}
            title="Get debugging suggestions"
          >
            <Zap className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Debug Controls */}
      <div className="flex items-center gap-1 p-2 border-b bg-muted/20">
        {!debugSession ? (
          <Button variant="outline" size="sm" onClick={startDebugSession}>
            <Play className="h-3 w-3 mr-1" />
            Start
          </Button>
        ) : (
          <>
            {debugSession.status === "running" ? (
              <Button variant="outline" size="sm" onClick={pauseDebugSession}>
                <Pause className="h-3 w-3" />
              </Button>
            ) : (
              <Button variant="outline" size="sm" onClick={continueExecution}>
                <Play className="h-3 w-3" />
              </Button>
            )}
            <Button variant="outline" size="sm" onClick={stopDebugSession}>
              <Square className="h-3 w-3" />
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <Button 
              variant="outline" 
              size="sm" 
              onClick={stepOver}
              disabled={debugSession.status !== "paused"}
            >
              <StepOver className="h-3 w-3" />
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={stepInto}
              disabled={debugSession.status !== "paused"}
            >
              <StepInto className="h-3 w-3" />
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={stepOut}
              disabled={debugSession.status !== "paused"}
            >
              <StepOut className="h-3 w-3" />
            </Button>
          </>
        )}
      </div>

      <Tabs value={selectedTab} onValueChange={(value: any) => setSelectedTab(value)} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-5 mx-2 mt-2">
          <TabsTrigger value="breakpoints" className="text-xs">Breakpoints</TabsTrigger>
          <TabsTrigger value="callstack" className="text-xs">Call Stack</TabsTrigger>
          <TabsTrigger value="variables" className="text-xs">Variables</TabsTrigger>
          <TabsTrigger value="watch" className="text-xs">Watch</TabsTrigger>
          <TabsTrigger value="console" className="text-xs">Console</TabsTrigger>
        </TabsList>

        <div className="flex-1 overflow-hidden">
          {/* Breakpoints Tab */}
          <TabsContent value="breakpoints" className="h-full mt-2 mx-2">
            <ScrollArea className="h-full">
              <div className="space-y-2">
                {breakpoints.map((breakpoint) => (
                  <div
                    key={breakpoint.id}
                    className="flex items-center gap-2 p-2 rounded-md border bg-muted/30"
                  >
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={() => toggleBreakpoint(breakpoint.id)}
                    >
                      <Circle className={cn(
                        "h-3 w-3",
                        breakpoint.enabled ? "fill-red-500 text-red-500" : "text-muted-foreground"
                      )} />
                    </Button>
                    <div className="flex-1 min-w-0">
                      <div className="font-mono text-xs truncate">
                        {breakpoint.file}:{breakpoint.line}
                      </div>
                      {breakpoint.condition && (
                        <div className="text-xs text-muted-foreground">
                          Condition: {breakpoint.condition}
                        </div>
                      )}
                      {breakpoint.logMessage && (
                        <div className="text-xs text-muted-foreground">
                          Log: {breakpoint.logMessage}
                        </div>
                      )}
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {breakpoint.hitCount}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeBreakpoint(breakpoint.id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
                
                {breakpoints.length === 0 && (
                  <div className="text-center text-muted-foreground py-8">
                    <Target className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No breakpoints set</p>
                    <p className="text-xs">Click in the editor gutter to add breakpoints</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Call Stack Tab */}
          <TabsContent value="callstack" className="h-full mt-2 mx-2">
            <ScrollArea className="h-full">
              <div className="space-y-1">
                {callStack.map((frame) => (
                  <div
                    key={frame.id}
                    className={cn(
                      "flex items-center gap-2 p-2 rounded-md cursor-pointer",
                      selectedFrame === frame.id 
                        ? "bg-primary/20 border border-primary/30" 
                        : "bg-muted/30 hover:bg-muted/50"
                    )}
                    onClick={() => setSelectedFrame(frame.id)}
                  >
                    <ArrowRight className="h-3 w-3 text-muted-foreground" />
                    <div className="flex-1 min-w-0">
                      <div className="font-mono text-sm font-medium">
                        {frame.name}
                      </div>
                      <div className="font-mono text-xs text-muted-foreground truncate">
                        {frame.file}:{frame.line}:{frame.column}
                      </div>
                    </div>
                  </div>
                ))}
                
                {callStack.length === 0 && (
                  <div className="text-center text-muted-foreground py-8">
                    <List className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No call stack available</p>
                    <p className="text-xs">Start debugging to see the call stack</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Variables Tab */}
          <TabsContent value="variables" className="h-full mt-2 mx-2">
            <ScrollArea className="h-full">
              <div className="space-y-1">
                {variables.map((variable) => renderVariable(variable))}
                
                {variables.length === 0 && (
                  <div className="text-center text-muted-foreground py-8">
                    <Variable className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No variables in scope</p>
                    <p className="text-xs">Variables will appear when debugging</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Watch Tab */}
          <TabsContent value="watch" className="h-full mt-2 mx-2">
            <div className="h-full flex flex-col">
              <div className="flex gap-2 mb-3">
                <Input
                  placeholder="Enter expression to watch..."
                  value={newWatchExpression}
                  onChange={(e) => setNewWatchExpression(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && addWatchExpression()}
                  className="flex-1"
                />
                <Button size="sm" onClick={addWatchExpression}>
                  <Eye className="h-3 w-3" />
                </Button>
              </div>
              
              <ScrollArea className="flex-1">
                <div className="space-y-2">
                  {watchExpressions.map((watch) => (
                    <div
                      key={watch.id}
                      className="flex items-center gap-2 p-2 rounded-md border bg-muted/30"
                    >
                      <Eye className="h-3 w-3 text-muted-foreground" />
                      <div className="flex-1 min-w-0">
                        <div className="font-mono text-sm">
                          {watch.expression}
                        </div>
                        <div className="font-mono text-xs text-muted-foreground">
                          {watch.error ? (
                            <span className="text-red-500">{watch.error}</span>
                          ) : (
                            String(watch.value)
                          )}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeWatchExpression(watch.id)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                  
                  {watchExpressions.length === 0 && (
                    <div className="text-center text-muted-foreground py-8">
                      <Eye className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No watch expressions</p>
                      <p className="text-xs">Add expressions to monitor their values</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>

          {/* Console Tab */}
          <TabsContent value="console" className="h-full mt-2 mx-2">
            <ScrollArea className="h-full">
              <div className="space-y-1 font-mono text-xs">
                {consoleOutput.map((output, index) => (
                  <div key={index} className="flex gap-2 p-1">
                    <span className="text-muted-foreground text-xs">
                      {output.timestamp.toLocaleTimeString()}
                    </span>
                    <span className={cn(
                      output.type === 'error' && "text-red-500",
                      output.type === 'warn' && "text-yellow-500",
                      output.type === 'log' && "text-foreground"
                    )}>
                      {output.message}
                    </span>
                  </div>
                ))}
                
                {consoleOutput.length === 0 && (
                  <div className="text-center text-muted-foreground py-8">
                    <Terminal className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No console output</p>
                    <p className="text-xs">Debug output will appear here</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </div>
      </Tabs>

      {/* Debug Settings */}
      <Collapsible>
        <CollapsibleTrigger asChild>
          <Button variant="ghost" size="sm" className="w-full justify-between p-2 border-t">
            <div className="flex items-center gap-2">
              <Settings className="h-3 w-3" />
              <span className="text-xs">Debug Settings</span>
            </div>
            <ChevronRight className="h-3 w-3" />
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="p-3 border-t bg-muted/20">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="pause-exceptions" className="text-xs">
                Pause on Exceptions
              </Label>
              <Switch
                id="pause-exceptions"
                checked={debugSettings.pauseOnExceptions}
                onCheckedChange={(checked) => 
                  setDebugSettings(prev => ({...prev, pauseOnExceptions: checked}))
                }
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="pause-caught" className="text-xs">
                Pause on Caught Exceptions
              </Label>
              <Switch
                id="pause-caught"
                checked={debugSettings.pauseOnCaughtExceptions}
                onCheckedChange={(checked) => 
                  setDebugSettings(prev => ({...prev, pauseOnCaughtExceptions: checked}))
                }
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="show-internal" className="text-xs">
                Show Internal Frames
              </Label>
              <Switch
                id="show-internal"
                checked={debugSettings.showInternalFrames}
                onCheckedChange={(checked) => 
                  setDebugSettings(prev => ({...prev, showInternalFrames: checked}))
                }
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="step-filtering" className="text-xs">
                Enable Step Filtering
              </Label>
              <Switch
                id="step-filtering"
                checked={debugSettings.enableStepFiltering}
                onCheckedChange={(checked) => 
                  setDebugSettings(prev => ({...prev, enableStepFiltering: checked}))
                }
              />
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
};

export default DebuggerPanel;