/**
 * Virtualized Idea Manager Component
 * 
 * Performance-optimized version of IdeaManager using react-window for large datasets
 */

import React, { useState, useCallback, useMemo, memo } from 'react';
import { FixedSizeList as List } from 'react-window';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { Idea, IdeaStatus } from '@/types/brainstorm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Lightbulb,
  Plus,
  Search,
  Filter,
  Edit2,
  Trash2,
  Link,
  Star,
  Clock,
  Tag,
  ArrowUpDown,
  Loader2,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useDebounce } from '@/hooks/useDebounce';

interface VirtualizedIdeaManagerProps {
  sessionId: string;
  className?: string;
  onIdeaSelect?: (idea: Idea) => void;
}

interface IdeaRowProps {
  index: number;
  style: React.CSSProperties;
  data: {
    ideas: Idea[];
    onEdit: (idea: Idea) => void;
    onDelete: (idea: Idea) => void;
    onLink: (idea: Idea) => void;
    onToggleStar: (idea: Idea) => void;
  };
}

// Memoized row component for performance
const IdeaRow = memo<IdeaRowProps>(({ index, style, data }) => {
  const { ideas, onEdit, onDelete, onLink, onToggleStar } = data;
  const idea = ideas[index];

  const priorityColors = {
    low: 'bg-gray-100 text-gray-800',
    medium: 'bg-blue-100 text-blue-800',
    high: 'bg-orange-100 text-orange-800',
    critical: 'bg-red-100 text-red-800',
  };

  const statusIcons = {
    active: <Lightbulb className="h-4 w-4" />,
    'in-progress': <Clock className="h-4 w-4" />,
    completed: <Star className="h-4 w-4" />,
    archived: null,
  };

  return (
    <div style={style} className="px-4">
      <Card className="mb-2 hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                {statusIcons[idea.status]}
                <h4 className="font-medium text-sm line-clamp-2">{idea.content}</h4>
              </div>
              
              <div className="flex items-center gap-2 flex-wrap">
                <Badge variant="secondary" className={cn('text-xs', priorityColors[idea.priority])}>
                  {idea.priority}
                </Badge>
                
                {idea.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    <Tag className="h-3 w-3 mr-1" />
                    {tag}
                  </Badge>
                ))}
                
                {idea.linkedIdeas.length > 0 && (
                  <Badge variant="outline" className="text-xs">
                    <Link className="h-3 w-3 mr-1" />
                    {idea.linkedIdeas.length} linked
                  </Badge>
                )}
              </div>
            </div>
            
            <div className="flex items-center gap-1 ml-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onToggleStar(idea)}
                className="h-8 w-8 p-0"
              >
                <Star
                  className={cn(
                    'h-4 w-4',
                    idea.status === 'completed' ? 'fill-yellow-400 text-yellow-400' : ''
                  )}
                />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit(idea)}
                className="h-8 w-8 p-0"
              >
                <Edit2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onLink(idea)}
                className="h-8 w-8 p-0"
              >
                <Link className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(idea)}
                className="h-8 w-8 p-0 text-destructive"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
});

IdeaRow.displayName = 'IdeaRow';

export const VirtualizedIdeaManager: React.FC<VirtualizedIdeaManagerProps> = memo(({
  sessionId,
  className,
  onIdeaSelect,
}) => {
  const { getIdeasBySession, addIdea, updateIdea, deleteIdea, linkIdeas } = useBrainstormStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'date' | 'priority'>('date');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingIdea, setEditingIdea] = useState<Idea | null>(null);
  const [loading, setLoading] = useState(false);

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Get and filter ideas with memoization
  const filteredIdeas = useMemo(() => {
    let ideas = getIdeasBySession(sessionId);

    // Apply search filter
    if (debouncedSearchTerm) {
      const searchLower = debouncedSearchTerm.toLowerCase();
      ideas = ideas.filter(
        (idea) =>
          idea.content.toLowerCase().includes(searchLower) ||
          idea.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Apply priority filter
    if (filterPriority !== 'all') {
      ideas = ideas.filter((idea) => idea.priority === filterPriority);
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      ideas = ideas.filter((idea) => idea.status === filterStatus);
    }

    // Sort ideas
    ideas.sort((a, b) => {
      if (sortBy === 'priority') {
        const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      }
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    return ideas;
  }, [sessionId, debouncedSearchTerm, filterPriority, filterStatus, sortBy, getIdeasBySession]);

  const handleEdit = useCallback((idea: Idea) => {
    setEditingIdea(idea);
    setShowAddDialog(true);
  }, []);

  const handleDelete = useCallback((idea: Idea) => {
    if (confirm('Are you sure you want to delete this idea?')) {
      deleteIdea(idea.id);
    }
  }, [deleteIdea]);

  const handleLink = useCallback((idea: Idea) => {
    // In a real implementation, show a dialog to select ideas to link
    console.log('Link idea:', idea);
  }, []);

  const handleToggleStar = useCallback((idea: Idea) => {
    updateIdea(idea.id, {
      status: idea.status === 'completed' ? 'active' : 'completed',
    });
  }, [updateIdea]);

  const handleSaveIdea = useCallback(async (formData: any) => {
    setLoading(true);
    try {
      if (editingIdea) {
        updateIdea(editingIdea.id, formData);
      } else {
        addIdea({
          ...formData,
          sessionId,
          id: Date.now().toString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          linkedIdeas: [],
        });
      }
      setShowAddDialog(false);
      setEditingIdea(null);
    } finally {
      setLoading(false);
    }
  }, [editingIdea, sessionId, addIdea, updateIdea]);

  // Memoized item data for react-window
  const itemData = useMemo(
    () => ({
      ideas: filteredIdeas,
      onEdit: handleEdit,
      onDelete: handleDelete,
      onLink: handleLink,
      onToggleStar: handleToggleStar,
    }),
    [filteredIdeas, handleEdit, handleDelete, handleLink, handleToggleStar]
  );

  const stats = useMemo(() => {
    const total = filteredIdeas.length;
    const byPriority = filteredIdeas.reduce((acc, idea) => {
      acc[idea.priority] = (acc[idea.priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    const completed = filteredIdeas.filter(i => i.status === 'completed').length;
    
    return { total, byPriority, completed };
  }, [filteredIdeas]);

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Ideas</CardTitle>
            <CardDescription>
              {stats.total} ideas • {stats.completed} completed
            </CardDescription>
          </div>
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Idea
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{editingIdea ? 'Edit' : 'Add New'} Idea</DialogTitle>
                <DialogDescription>
                  Capture and organize your brainstorming ideas
                </DialogDescription>
              </DialogHeader>
              <IdeaForm
                idea={editingIdea}
                onSave={handleSaveIdea}
                loading={loading}
              />
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="flex flex-wrap gap-2 mb-4">
          <div className="flex-1 min-w-[200px]">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search ideas..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
          
          <Select value={filterPriority} onValueChange={setFilterPriority}>
            <SelectTrigger className="w-[140px]">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priorities</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="in-progress">In Progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="archived">Archived</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            onClick={() => setSortBy(sortBy === 'date' ? 'priority' : 'date')}
          >
            <ArrowUpDown className="h-4 w-4 mr-2" />
            {sortBy === 'date' ? 'Date' : 'Priority'}
          </Button>
        </div>

        {/* Priority stats */}
        <div className="flex gap-2 mb-4">
          {Object.entries(stats.byPriority).map(([priority, count]) => (
            <Badge key={priority} variant="secondary">
              {priority}: {count}
            </Badge>
          ))}
        </div>

        {/* Virtualized list */}
        {filteredIdeas.length > 0 ? (
          <List
            height={600}
            itemCount={filteredIdeas.length}
            itemSize={120}
            width="100%"
            itemData={itemData}
            className="scrollbar-thin"
          >
            {IdeaRow}
          </List>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            {debouncedSearchTerm || filterPriority !== 'all' || filterStatus !== 'all'
              ? 'No ideas match your filters'
              : 'No ideas yet. Click "Add Idea" to get started!'}
          </div>
        )}
      </CardContent>
    </Card>
  );
});

VirtualizedIdeaManager.displayName = 'VirtualizedIdeaManager';

// Idea form component
const IdeaForm: React.FC<{
  idea?: Idea | null;
  onSave: (data: any) => void;
  loading?: boolean;
}> = ({ idea, onSave, loading }) => {
  const [content, setContent] = useState(idea?.content || '');
  const [priority, setPriority] = useState(idea?.priority || 'medium');
  const [status, setStatus] = useState(idea?.status || 'active');
  const [tags, setTags] = useState(idea?.tags.join(', ') || '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      content,
      priority,
      status,
      tags: tags.split(',').map(t => t.trim()).filter(Boolean),
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="text-sm font-medium">Idea Content</label>
        <Textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          placeholder="Describe your idea..."
          className="mt-1"
          rows={3}
          required
        />
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="text-sm font-medium">Priority</label>
          <Select value={priority} onValueChange={setPriority}>
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <label className="text-sm font-medium">Status</label>
          <Select value={status} onValueChange={setStatus}>
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="in-progress">In Progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="archived">Archived</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div>
        <label className="text-sm font-medium">Tags (comma-separated)</label>
        <Input
          value={tags}
          onChange={(e) => setTags(e.target.value)}
          placeholder="ui, feature, bug-fix"
          className="mt-1"
        />
      </div>
      
      <div className="flex justify-end gap-2">
        <Button type="submit" disabled={loading || !content}>
          {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
          {idea ? 'Update' : 'Add'} Idea
        </Button>
      </div>
    </form>
  );
};