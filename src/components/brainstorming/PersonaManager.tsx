/**
 * AI Persona Manager Component
 * 
 * Allows switching between different AI personas for varied brainstorming perspectives
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Brain,
  Sun,
  AlertTriangle,
  GraduationCap,
  Zap,
  Plus,
  Edit,
  Trash2,
  Check,
  X,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { AIPersona } from '@/types/brainstorm';

interface PersonaManagerProps {
  className?: string;
  onPersonaChange?: (persona: AIPersona) => void;
}

const PERSONA_ICONS = {
  brain: Brain,
  sun: Sun,
  'alert-triangle': AlertTriangle,
  'graduation-cap': GraduationCap,
  zap: Zap,
  sparkles: Sparkles,
};

export const PersonaManager: React.FC<PersonaManagerProps> = ({
  className,
  onPersonaChange
}) => {
  const { 
    personas, 
    currentPersona, 
    setCurrentPersona, 
    updatePersona 
  } = useBrainstormStore();
  
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingPersona, setEditingPersona] = useState<AIPersona | null>(null);
  const [newPersona, setNewPersona] = useState<Partial<AIPersona>>({
    name: '',
    description: '',
    systemPrompt: '',
    icon: 'brain',
    color: '#6366f1'
  });

  // Handle persona selection
  const handlePersonaSelect = (persona: AIPersona) => {
    setCurrentPersona(persona);
    onPersonaChange?.(persona);
  };

  // Start editing a persona
  const startEditing = (persona: AIPersona) => {
    setEditingPersona(persona);
    setNewPersona({ ...persona });
    setShowCreateDialog(true);
  };

  // Save persona (create or update)
  const savePersona = () => {
    if (!newPersona.name || !newPersona.description || !newPersona.systemPrompt) {
      return;
    }

    if (editingPersona) {
      // Update existing persona
      updatePersona(editingPersona.id, newPersona as Partial<AIPersona>);
    } else {
      // Create new persona - would need to add this to store
      console.log('Create new persona:', newPersona);
    }

    resetForm();
  };

  // Reset form
  const resetForm = () => {
    setShowCreateDialog(false);
    setEditingPersona(null);
    setNewPersona({
      name: '',
      description: '',
      systemPrompt: '',
      icon: 'brain',
      color: '#6366f1'
    });
  };

  // Get persona icon component
  const getPersonaIcon = (iconName: string) => {
    return PERSONA_ICONS[iconName as keyof typeof PERSONA_ICONS] || Brain;
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">AI Personas</h3>
          <p className="text-sm text-muted-foreground">
            Switch perspectives to get diverse insights
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowCreateDialog(true)}
        >
          <Plus className="h-4 w-4 mr-1" />
          Add Persona
        </Button>
      </div>

      {/* Current Persona Display */}
      <Card className="border-primary/50 bg-primary/5">
        <CardHeader className="pb-3">
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10" style={{ backgroundColor: currentPersona.color }}>
              <AvatarFallback className="text-white">
                {React.createElement(getPersonaIcon(currentPersona.icon), { className: "h-5 w-5" })}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-base">{currentPersona.name}</CardTitle>
              <CardDescription>{currentPersona.description}</CardDescription>
            </div>
            <Badge variant="secondary" className="ml-auto">
              Active
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Persona Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <AnimatePresence>
          {personas.map((persona) => {
            const IconComponent = getPersonaIcon(persona.icon);
            const isActive = persona.id === currentPersona.id;
            
            return (
              <motion.div
                key={persona.id}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.2 }}
              >
                <Card
                  className={cn(
                    "cursor-pointer transition-all hover:shadow-md",
                    isActive && "ring-2 ring-primary bg-primary/5"
                  )}
                  onClick={() => !isActive && handlePersonaSelect(persona)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <Avatar 
                        className="h-8 w-8 flex-shrink-0" 
                        style={{ backgroundColor: persona.color }}
                      >
                        <AvatarFallback className="text-white">
                          <IconComponent className="h-4 w-4" />
                        </AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h4 className="font-medium text-sm truncate">
                            {persona.name}
                          </h4>
                          {!isActive && (
                            <div className="flex gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  startEditing(persona);
                                }}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {persona.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {/* Create/Edit Persona Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingPersona ? 'Edit Persona' : 'Create New Persona'}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={newPersona.name}
                onChange={(e) => setNewPersona({ ...newPersona, name: e.target.value })}
                placeholder="e.g., Creative Thinker"
              />
            </div>
            
            <div>
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={newPersona.description}
                onChange={(e) => setNewPersona({ ...newPersona, description: e.target.value })}
                placeholder="Brief description of this persona's perspective"
              />
            </div>
            
            <div>
              <Label htmlFor="systemPrompt">System Prompt</Label>
              <Textarea
                id="systemPrompt"
                value={newPersona.systemPrompt}
                onChange={(e) => setNewPersona({ ...newPersona, systemPrompt: e.target.value })}
                placeholder="Detailed instructions for how this AI persona should behave..."
                rows={4}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="icon">Icon</Label>
                <select
                  id="icon"
                  value={newPersona.icon}
                  onChange={(e) => setNewPersona({ ...newPersona, icon: e.target.value })}
                  className="w-full p-2 border rounded-md"
                >
                  {Object.keys(PERSONA_ICONS).map(icon => (
                    <option key={icon} value={icon}>
                      {icon.replace('-', ' ')}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <Label htmlFor="color">Color</Label>
                <Input
                  id="color"
                  type="color"
                  value={newPersona.color}
                  onChange={(e) => setNewPersona({ ...newPersona, color: e.target.value })}
                />
              </div>
            </div>
          </div>
          
          <div className="flex justify-end gap-2 mt-6">
            <Button variant="outline" onClick={resetForm}>
              <X className="h-4 w-4 mr-1" />
              Cancel
            </Button>
            <Button onClick={savePersona}>
              <Check className="h-4 w-4 mr-1" />
              {editingPersona ? 'Update' : 'Create'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};