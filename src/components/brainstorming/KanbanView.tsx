import React, { useState } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { 
  Plus, 
  MoreVertical, 
  Edit2, 
  Trash2, 
  Tag,
  AlertCircle,
  CheckCircle,
  Clock,
  Archive
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { Idea, IdeaStatus, Priority } from '@/types/brainstorm';

interface KanbanViewProps {
  sessionId: string;
  className?: string;
}

interface KanbanColumn {
  id: IdeaStatus;
  title: string;
  icon: React.ComponentType<any>;
  color: string;
}

const columns: KanbanColumn[] = [
  {
    id: IdeaStatus.TO_EXPLORE,
    title: 'To Explore',
    icon: Clock,
    color: 'bg-gray-100 dark:bg-gray-800 border-gray-300',
  },
  {
    id: IdeaStatus.IN_PROGRESS,
    title: 'In Progress',
    icon: AlertCircle,
    color: 'bg-blue-50 dark:bg-blue-900/20 border-blue-300',
  },
  {
    id: IdeaStatus.VALIDATED,
    title: 'Validated',
    icon: CheckCircle,
    color: 'bg-green-50 dark:bg-green-900/20 border-green-300',
  },
  {
    id: IdeaStatus.ARCHIVED,
    title: 'Archived',
    icon: Archive,
    color: 'bg-gray-50 dark:bg-gray-900 border-gray-200',
  },
];

const IdeaCard: React.FC<{
  idea: Idea;
  index: number;
  onEdit: (idea: Idea) => void;
  onDelete: (ideaId: string) => void;
}> = ({ idea, index, onEdit, onDelete }) => {
  const priorityColors = {
    [Priority.LOW]: 'bg-gray-100 text-gray-700',
    [Priority.MEDIUM]: 'bg-yellow-100 text-yellow-700',
    [Priority.HIGH]: 'bg-orange-100 text-orange-700',
    [Priority.CRITICAL]: 'bg-red-100 text-red-700',
  };

  return (
    <Draggable draggableId={idea.id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
        >
          <Card
            className={cn(
              'mb-2 cursor-move transition-all',
              snapshot.isDragging && 'shadow-lg rotate-2 scale-105'
            )}
          >
          <CardContent className="p-3">
            <div className="flex items-start justify-between gap-2">
              <p className="text-sm flex-1">{idea.content}</p>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreVertical className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onEdit(idea)}>
                    <Edit2 className="h-3 w-3 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => onDelete(idea.id)}
                    className="text-red-600"
                  >
                    <Trash2 className="h-3 w-3 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            
            <div className="flex items-center gap-2 mt-2">
              {idea.priority && (
                <Badge 
                  variant="secondary" 
                  className={cn('text-xs', priorityColors[idea.priority])}
                >
                  {idea.priority}
                </Badge>
              )}
              
              {idea.tags && idea.tags.length > 0 && (
                <div className="flex items-center gap-1">
                  <Tag className="h-3 w-3 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">
                    {idea.tags.length}
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
        </div>
      )}
    </Draggable>
  );
};

export const KanbanView: React.FC<KanbanViewProps> = ({ sessionId, className }) => {
  const { 
    getIdeasBySession, 
    updateIdea, 
    deleteIdea, 
    addIdea 
  } = useBrainstormStore();
  
  const [newIdeaContent, setNewIdeaContent] = useState<{ [key: string]: string }>({});
  // const [editingIdea, setEditingIdea] = useState<Idea | null>(null);
  
  const ideas = getIdeasBySession(sessionId);

  const getIdeasByStatus = (status: IdeaStatus) => {
    return ideas.filter(idea => idea.status === status);
  };

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const { draggableId, destination } = result;
    const newStatus = destination.droppableId as IdeaStatus;

    updateIdea(draggableId, { status: newStatus });
  };

  const handleAddIdea = (status: IdeaStatus) => {
    const content = newIdeaContent[status]?.trim();
    if (!content) return;

    addIdea(sessionId, content, { status });

    setNewIdeaContent({ ...newIdeaContent, [status]: '' });
  };

  const handleDeleteIdea = (ideaId: string) => {
    deleteIdea(ideaId);
  };

  const handleEditIdea = (idea: Idea) => {
    // setEditingIdea(idea);
    // TODO: Open edit modal
    console.log('Edit idea:', idea);
  };

  return (
    <div className={cn('flex flex-col h-full bg-gray-50 dark:bg-gray-900', className)}>
      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="flex gap-4 p-4 h-full overflow-x-auto">
          {columns.map((column) => {
            const columnIdeas = getIdeasByStatus(column.id);
            const Icon = column.icon;

            return (
              <div
                key={column.id}
                className={cn(
                  'flex-shrink-0 w-80 flex flex-col rounded-lg border-2',
                  column.color
                )}
              >
                <div className="p-4 border-b">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Icon className="h-5 w-5" />
                      <h3 className="font-semibold">{column.title}</h3>
                      <Badge variant="secondary" className="ml-2">
                        {columnIdeas.length}
                      </Badge>
                    </div>
                  </div>
                </div>

                <Droppable droppableId={column.id}>
                  {(provided, snapshot) => (
                    <ScrollArea
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className={cn(
                        'flex-1 p-2',
                        snapshot.isDraggingOver && 'bg-accent/50'
                      )}
                    >
                      {columnIdeas.map((idea, index) => (
                        <IdeaCard
                          key={idea.id}
                          idea={idea}
                          index={index}
                          onEdit={handleEditIdea}
                          onDelete={handleDeleteIdea}
                        />
                      ))}
                      {provided.placeholder}
                    </ScrollArea>
                  )}
                </Droppable>

                <div className="p-2 border-t">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add an idea..."
                      value={newIdeaContent[column.id] || ''}
                      onChange={(e) => setNewIdeaContent({
                        ...newIdeaContent,
                        [column.id]: e.target.value
                      })}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          handleAddIdea(column.id);
                        }
                      }}
                      className="flex-1"
                    />
                    <Button
                      size="sm"
                      onClick={() => handleAddIdea(column.id)}
                      disabled={!newIdeaContent[column.id]?.trim()}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </DragDropContext>
    </div>
  );
};