/**
 * Cross-Session Workflow Manager
 * 
 * UI component for managing transitions between brainstorm → <PERSON> → orchestra sessions
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowRight,
  Brain,
  Code,
  Users,
  Play,
  Pause,
  Archive,
  Plus,
  Settings,
  GitBranch,
  FileText,
  Zap,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { crossSessionOrchestrator, type CrossSessionWorkflow, type SessionReference } from '@/lib/cross-session-orchestrator';
import { brainstormStore } from '@/stores/brainstormStore';
import type { ModelType } from '@/types/session';
import { cn } from '@/lib/utils';

interface CrossSessionWorkflowManagerProps {
  onNavigateToSession?: (sessionType: 'brainstorm' | 'claude' | 'orchestra', sessionId: string) => void;
}

export const CrossSessionWorkflowManager: React.FC<CrossSessionWorkflowManagerProps> = ({
  onNavigateToSession
}) => {
  const [workflows, setWorkflows] = useState<CrossSessionWorkflow[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState<CrossSessionWorkflow | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [loading, setLoading] = useState(false);

  // Form state for creating workflows
  const [newWorkflowName, setNewWorkflowName] = useState('');
  const [newWorkflowDescription, setNewWorkflowDescription] = useState('');
  const [selectedBrainstormSession, setSelectedBrainstormSession] = useState('');
  const [projectPath, setProjectPath] = useState('');
  const [claudeModel, setClaudeModel] = useState<ModelType>('claude-3-5-sonnet-20241022');
  const [orchestrationMode, setOrchestrationMode] = useState<'review' | 'parallel' | 'enhancement'>('review');

  // Load workflows on mount
  useEffect(() => {
    loadWorkflows();
  }, []);

  const loadWorkflows = () => {
    const allWorkflows = crossSessionOrchestrator.listWorkflows();
    setWorkflows(allWorkflows);
  };

  const handleCreateFullWorkflow = async () => {
    if (!newWorkflowName.trim() || !selectedBrainstormSession || !projectPath.trim()) {
      return;
    }

    try {
      setLoading(true);
      
      const workflow = await crossSessionOrchestrator.executeFullWorkflow(
        selectedBrainstormSession,
        {
          workflowName: newWorkflowName,
          projectPath,
          claudeModel,
          orchestrationMode
        }
      );

      setWorkflows(prev => [...prev, workflow]);
      setShowCreateDialog(false);
      resetForm();
    } catch (error) {
      console.error('Failed to create workflow:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTransitionToClaude = async (workflowId: string, brainstormSessionId: string) => {
    try {
      setLoading(true);
      
      await crossSessionOrchestrator.transitionBrainstormToClaude(
        workflowId,
        brainstormSessionId,
        { projectPath }
      );
      
      loadWorkflows();
    } catch (error) {
      console.error('Failed to transition to Claude:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTransitionToOrchestra = async (workflowId: string, claudeSessionId: string) => {
    try {
      setLoading(true);
      
      await crossSessionOrchestrator.transitionClaudeToOrchestra(
        workflowId,
        claudeSessionId,
        { orchestrationMode }
      );
      
      loadWorkflows();
    } catch (error) {
      console.error('Failed to transition to Orchestra:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setNewWorkflowName('');
    setNewWorkflowDescription('');
    setSelectedBrainstormSession('');
    setProjectPath('');
    setClaudeModel('claude-3-5-sonnet-20241022');
    setOrchestrationMode('review');
  };

  const getSessionIcon = (type: SessionReference['type']) => {
    switch (type) {
      case 'brainstorm': return Brain;
      case 'claude': return Code;
      case 'orchestra': return Users;
      default: return FileText;
    }
  };

  const getStatusIcon = (status: SessionReference['status']) => {
    switch (status) {
      case 'active': return Play;
      case 'completed': return CheckCircle;
      case 'pending': return Clock;
      case 'failed': return AlertCircle;
      default: return Clock;
    }
  };

  const getStatusColor = (status: SessionReference['status']) => {
    switch (status) {
      case 'active': return 'text-blue-500';
      case 'completed': return 'text-green-500';
      case 'pending': return 'text-yellow-500';
      case 'failed': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const calculateWorkflowProgress = (workflow: CrossSessionWorkflow): number => {
    if (workflow.sessions.length === 0) return 0;
    const completedSessions = workflow.sessions.filter(s => s.status === 'completed').length;
    return (completedSessions / workflow.sessions.length) * 100;
  };

  const renderWorkflowCard = (workflow: CrossSessionWorkflow) => (
    <Card 
      key={workflow.id} 
      className={cn(
        "cursor-pointer transition-all hover:shadow-md",
        selectedWorkflow?.id === workflow.id && "ring-2 ring-primary"
      )}
      onClick={() => setSelectedWorkflow(workflow)}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{workflow.name}</CardTitle>
          <Badge variant={workflow.status === 'active' ? 'default' : 'secondary'}>
            {workflow.status}
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground">{workflow.description}</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Progress bar */}
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Progress</span>
              <span>{Math.round(calculateWorkflowProgress(workflow))}%</span>
            </div>
            <Progress value={calculateWorkflowProgress(workflow)} className="h-2" />
          </div>

          {/* Session flow */}
          <div className="flex items-center justify-between">
            {workflow.sessions.map((session, index) => {
              const Icon = getSessionIcon(session.type);
              const StatusIcon = getStatusIcon(session.status);
              
              return (
                <React.Fragment key={session.id}>
                  <div className="flex flex-col items-center gap-1">
                    <div className={cn(
                      "relative p-2 rounded-full border-2",
                      session.status === 'active' ? "border-primary bg-primary/10" : "border-border"
                    )}>
                      <Icon className="h-4 w-4" />
                      <StatusIcon className={cn(
                        "absolute -top-1 -right-1 h-3 w-3",
                        getStatusColor(session.status)
                      )} />
                    </div>
                    <span className="text-xs capitalize">{session.type}</span>
                  </div>
                  {index < workflow.sessions.length - 1 && (
                    <ArrowRight className="h-4 w-4 text-muted-foreground" />
                  )}
                </React.Fragment>
              );
            })}
          </div>

          {/* Metadata */}
          <div className="text-xs text-muted-foreground">
            Created {workflow.createdAt.toLocaleDateString()} • 
            {workflow.sessions.length} session{workflow.sessions.length !== 1 ? 's' : ''} • 
            {workflow.transitions.length} transition{workflow.transitions.length !== 1 ? 's' : ''}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const renderWorkflowDetails = (workflow: CrossSessionWorkflow) => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">{workflow.name}</h3>
        <p className="text-muted-foreground">{workflow.description}</p>
      </div>

      {/* Sessions */}
      <div>
        <h4 className="font-medium mb-3">Sessions</h4>
        <div className="space-y-2">
          {workflow.sessions.map((session) => {
            const Icon = getSessionIcon(session.type);
            const StatusIcon = getStatusIcon(session.status);
            
            return (
              <Card key={session.id} className="p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <Icon className="h-5 w-5" />
                      <StatusIcon className={cn(
                        "absolute -top-1 -right-1 h-3 w-3",
                        getStatusColor(session.status)
                      )} />
                    </div>
                    <div>
                      <p className="font-medium">{session.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {session.projectPath && `${session.projectPath} • `}
                        {session.createdAt.toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="capitalize">
                      {session.status}
                    </Badge>
                    {onNavigateToSession && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onNavigateToSession(session.type, session.id)}
                      >
                        Open
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Transitions */}
      <div>
        <h4 className="font-medium mb-3">Transitions</h4>
        <div className="space-y-2">
          {workflow.transitions.map((transition) => (
            <Card key={transition.id} className="p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <GitBranch className="h-4 w-4" />
                  <span className="font-medium">
                    {workflow.sessions.find(s => s.id === transition.fromSession)?.type} →{' '}
                    {workflow.sessions.find(s => s.id === transition.toSession)?.type}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="capitalize">
                    {transition.status}
                  </Badge>
                  {transition.executedAt && (
                    <span className="text-xs text-muted-foreground">
                      {transition.executedAt.toLocaleString()}
                    </span>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          onClick={() => crossSessionOrchestrator.archiveWorkflow(workflow.id)}
        >
          <Archive className="h-4 w-4 mr-2" />
          Archive
        </Button>
      </div>
    </div>
  );

  const brainstormSessions = brainstormStore.getAllSessions();

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b">
        <div>
          <h1 className="text-2xl font-bold">Cross-Session Workflows</h1>
          <p className="text-muted-foreground">
            Manage workflows that span brainstorming, coding, and orchestration
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Workflow
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create Cross-Session Workflow</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="workflow-name">Workflow Name</Label>
                  <Input
                    id="workflow-name"
                    value={newWorkflowName}
                    onChange={(e) => setNewWorkflowName(e.target.value)}
                    placeholder="e.g., Feature Development Workflow"
                  />
                </div>
                <div>
                  <Label htmlFor="project-path">Project Path</Label>
                  <Input
                    id="project-path"
                    value={projectPath}
                    onChange={(e) => setProjectPath(e.target.value)}
                    placeholder="/path/to/project"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="workflow-description">Description</Label>
                <Textarea
                  id="workflow-description"
                  value={newWorkflowDescription}
                  onChange={(e) => setNewWorkflowDescription(e.target.value)}
                  placeholder="Describe the workflow purpose and goals"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="brainstorm-session">Source Brainstorm Session</Label>
                  <Select value={selectedBrainstormSession} onValueChange={setSelectedBrainstormSession}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select brainstorm session" />
                    </SelectTrigger>
                    <SelectContent>
                      {brainstormSessions.map((session) => (
                        <SelectItem key={session.id} value={session.id}>
                          {session.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="claude-model">Claude Model</Label>
                  <Select value={claudeModel} onValueChange={(value: ModelType) => setClaudeModel(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="claude-3-5-sonnet-20241022">Claude 3.5 Sonnet</SelectItem>
                      <SelectItem value="claude-3-5-haiku-20241022">Claude 3.5 Haiku</SelectItem>
                      <SelectItem value="claude-3-opus-20240229">Claude 3 Opus</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="orchestration-mode">Orchestration Mode</Label>
                <Select value={orchestrationMode} onValueChange={(value: any) => setOrchestrationMode(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="review">Review & Improve</SelectItem>
                    <SelectItem value="parallel">Parallel Development</SelectItem>
                    <SelectItem value="enhancement">Feature Enhancement</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateFullWorkflow} disabled={loading}>
                  {loading ? <Zap className="h-4 w-4 mr-2 animate-spin" /> : <Play className="h-4 w-4 mr-2" />}
                  Create Workflow
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Content */}
      <div className="flex-1 flex">
        {/* Workflows list */}
        <div className="w-1/2 p-6 border-r">
          <div className="space-y-4">
            {workflows.length === 0 ? (
              <div className="text-center py-12">
                <GitBranch className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium">No workflows yet</h3>
                <p className="text-muted-foreground mb-4">
                  Create your first cross-session workflow to connect brainstorming, coding, and orchestration
                </p>
                <Button onClick={() => setShowCreateDialog(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Workflow
                </Button>
              </div>
            ) : (
              <AnimatePresence>
                {workflows.map((workflow) => (
                  <motion.div
                    key={workflow.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                  >
                    {renderWorkflowCard(workflow)}
                  </motion.div>
                ))}
              </AnimatePresence>
            )}
          </div>
        </div>

        {/* Workflow details */}
        <div className="w-1/2 p-6">
          {selectedWorkflow ? (
            renderWorkflowDetails(selectedWorkflow)
          ) : (
            <div className="flex items-center justify-center h-full text-muted-foreground">
              <div className="text-center">
                <Settings className="h-12 w-12 mx-auto mb-4" />
                <p>Select a workflow to view details</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CrossSessionWorkflowManager;