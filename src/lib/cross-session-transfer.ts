import { invoke } from '@tauri-apps/api/core';
import { Idea, IdeaCluster, BrainstormSession } from '@/types/brainstorm';

export interface TransferableData {
  type: 'idea' | 'cluster' | 'session' | 'selection';
  data: any;
  sourceSessionId: string;
  targetSessionId?: string;
  metadata?: {
    transferredAt: string;
    transferredBy?: string;
    notes?: string;
  };
}

export interface SessionLink {
  id: string;
  sourceSessionId: string;
  targetSessionId: string;
  linkType: 'parent-child' | 'related' | 'continuation' | 'fork';
  createdAt: string;
  metadata?: Record<string, any>;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  steps: WorkflowStep[];
  createdAt: string;
  updatedAt: string;
}

export interface WorkflowStep {
  id: string;
  name: string;
  description: string;
  sessionType: 'brainstorm' | 'claude' | 'custom';
  inputDataType?: string[];
  outputDataType?: string[];
  autoTransfer?: boolean;
}

export class CrossSessionTransfer {
  private static transferQueue: TransferableData[] = [];
  private static sessionLinks: Map<string, SessionLink[]> = new Map();

  // Queue data for transfer
  static queueForTransfer(data: TransferableData): void {
    this.transferQueue.push({
      ...data,
      metadata: {
        ...data.metadata,
        transferredAt: new Date().toISOString(),
      },
    });
  }

  // Get queued transfers
  static getQueuedTransfers(): TransferableData[] {
    return [...this.transferQueue];
  }

  // Clear transfer queue
  static clearQueue(): void {
    this.transferQueue = [];
  }

  // Transfer single idea to another session
  static async transferIdea(
    idea: Idea,
    sourceSessionId: string,
    targetSessionId: string,
    includeRelated: boolean = false
  ): Promise<void> {
    const transferData: TransferableData = {
      type: 'idea',
      data: idea,
      sourceSessionId,
      targetSessionId,
    };

    await invoke('transfer_session_data', { transferData });

    // If includeRelated, also transfer connected ideas
    if (includeRelated && idea.connections.length > 0) {
      // This would need access to the full idea list to find connected ideas
      console.log('Transfer related ideas:', idea.connections);
    }
  }

  // Transfer multiple ideas as a batch
  static async transferIdeas(
    ideas: Idea[],
    sourceSessionId: string,
    targetSessionId: string
  ): Promise<void> {
    const transferData: TransferableData = {
      type: 'selection',
      data: { ideas },
      sourceSessionId,
      targetSessionId,
    };

    await invoke('transfer_session_data', { transferData });
  }

  // Transfer entire cluster
  static async transferCluster(
    cluster: IdeaCluster,
    ideas: Idea[],
    sourceSessionId: string,
    targetSessionId: string
  ): Promise<void> {
    const clusterIdeas = ideas.filter(idea => cluster.ideaIds.includes(idea.id));
    
    const transferData: TransferableData = {
      type: 'cluster',
      data: {
        cluster,
        ideas: clusterIdeas,
      },
      sourceSessionId,
      targetSessionId,
    };

    await invoke('transfer_session_data', { transferData });
  }

  // Link two sessions
  static async linkSessions(
    sourceSessionId: string,
    targetSessionId: string,
    linkType: SessionLink['linkType'],
    metadata?: Record<string, any>
  ): Promise<SessionLink> {
    const link: SessionLink = {
      id: `link_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      sourceSessionId,
      targetSessionId,
      linkType,
      createdAt: new Date().toISOString(),
      metadata,
    };

    await invoke('create_session_link', { link });

    // Update local cache
    if (!this.sessionLinks.has(sourceSessionId)) {
      this.sessionLinks.set(sourceSessionId, []);
    }
    this.sessionLinks.get(sourceSessionId)!.push(link);

    return link;
  }

  // Get linked sessions
  static async getLinkedSessions(sessionId: string): Promise<SessionLink[]> {
    try {
      const links = await invoke<SessionLink[]>('get_session_links', { sessionId });
      return links;
    } catch (error) {
      console.error('Failed to get session links:', error);
      return [];
    }
  }

  // Create workflow from sessions
  static async createWorkflowFromSessions(
    sessionIds: string[],
    workflowName: string,
    description: string
  ): Promise<WorkflowTemplate> {
    const steps: WorkflowStep[] = sessionIds.map((sessionId, index) => ({
      id: `step_${index}`,
      name: `Step ${index + 1}`,
      description: `Process data from session ${sessionId}`,
      sessionType: 'brainstorm',
      inputDataType: index > 0 ? ['idea', 'cluster'] : undefined,
      outputDataType: ['idea', 'cluster'],
      autoTransfer: true,
    }));

    const workflow: WorkflowTemplate = {
      id: `workflow_${Date.now()}`,
      name: workflowName,
      description,
      steps,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await invoke('save_workflow_template', { workflow });
    return workflow;
  }

  // Apply workflow template
  static async applyWorkflowTemplate(
    templateId: string,
    startingSessionId?: string
  ): Promise<string[]> {
    const sessionIds = await invoke<string[]>('apply_workflow_template', {
      templateId,
      startingSessionId,
    });
    return sessionIds;
  }

  // Export session chain
  static async exportSessionChain(
    sessionIds: string[],
    format: 'json' | 'markdown'
  ): Promise<Blob> {
    const exportData = await invoke<string>('export_session_chain', {
      sessionIds,
      format,
    });

    const mimeType = format === 'json' ? 'application/json' : 'text/markdown';
    return new Blob([exportData], { type: mimeType });
  }

  // Import data from external source
  static async importExternalData(
    data: any,
    dataType: 'csv' | 'json' | 'markdown',
    targetSessionId: string
  ): Promise<void> {
    await invoke('import_external_data', {
      data,
      dataType,
      targetSessionId,
    });
  }

  // Get transfer history
  static async getTransferHistory(
    sessionId: string
  ): Promise<TransferableData[]> {
    try {
      const history = await invoke<TransferableData[]>('get_transfer_history', {
        sessionId,
      });
      return history;
    } catch (error) {
      console.error('Failed to get transfer history:', error);
      return [];
    }
  }

  // Merge sessions
  static async mergeSessions(
    sessionIds: string[],
    mergeStrategy: 'concat' | 'dedupe' | 'cluster'
  ): Promise<string> {
    const newSessionId = await invoke<string>('merge_sessions', {
      sessionIds,
      mergeStrategy,
    });
    return newSessionId;
  }

  // Fork session
  static async forkSession(
    sourceSessionId: string,
    forkName: string,
    includeData: {
      ideas?: boolean;
      clusters?: boolean;
      messages?: boolean;
      metadata?: boolean;
    } = { ideas: true, clusters: true, messages: false, metadata: true }
  ): Promise<string> {
    const newSessionId = await invoke<string>('fork_session', {
      sourceSessionId,
      forkName,
      includeData,
    });

    // Create a fork link
    await this.linkSessions(sourceSessionId, newSessionId, 'fork');

    return newSessionId;
  }

  // Clone idea to multiple sessions
  static async cloneIdeaToSessions(
    idea: Idea,
    sourceSessionId: string,
    targetSessionIds: string[]
  ): Promise<void> {
    await Promise.all(
      targetSessionIds.map(targetId =>
        this.transferIdea(idea, sourceSessionId, targetId)
      )
    );
  }

  // Get session graph (visual representation of linked sessions)
  static async getSessionGraph(
    rootSessionId: string,
    depth: number = 2
  ): Promise<{
    nodes: Array<{ id: string; label: string; type: string }>;
    edges: Array<{ source: string; target: string; label: string }>;
  }> {
    const graph = await invoke<any>('get_session_graph', {
      rootSessionId,
      depth,
    });
    return graph;
  }
}

// Helper functions for common transfer scenarios
export async function transferIdeaToClaudeSession(
  idea: Idea,
  sourceSessionId: string
): Promise<void> {
  // This would integrate with the Claude session system
  const claudeSessionId = await invoke<string>('get_active_claude_session');
  if (claudeSessionId) {
    await CrossSessionTransfer.transferIdea(idea, sourceSessionId, claudeSessionId);
  }
}

export async function createBrainstormingPipeline(
  stages: Array<{
    name: string;
    description: string;
    duration?: number;
  }>
): Promise<WorkflowTemplate> {
  const steps: WorkflowStep[] = stages.map((stage, index) => ({
    id: `stage_${index}`,
    name: stage.name,
    description: stage.description,
    sessionType: 'brainstorm',
    inputDataType: index > 0 ? ['idea'] : undefined,
    outputDataType: ['idea', 'cluster'],
    autoTransfer: true,
  }));

  const workflow: WorkflowTemplate = {
    id: `pipeline_${Date.now()}`,
    name: 'Brainstorming Pipeline',
    description: 'Multi-stage brainstorming workflow',
    steps,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  return workflow;
}