/**
 * Cross-Session Workflow Orchestrator
 * 
 * Manages transitions and relationships between different session types:
 * - Brainstorming sessions
 * - Claude Code sessions  
 * - Agent Orchestra sessions
 */

// import { sessionManager } from './session-manager';
import { invoke } from '@tauri-apps/api/core';
import type { SessionData, ModelType } from '@/types/session';
import type { BrainstormSession } from '@/types/brainstorm';

export interface CrossSessionWorkflow {
  id: string;
  name: string;
  description: string;
  sessions: SessionReference[];
  transitions: WorkflowTransition[];
  status: 'draft' | 'active' | 'completed' | 'archived';
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface SessionReference {
  id: string;
  type: 'brainstorm' | 'claude' | 'orchestra';
  name: string;
  projectPath?: string;
  status: 'pending' | 'active' | 'completed' | 'failed';
  metadata: Record<string, any>;
  createdAt: Date;
}

export interface WorkflowTransition {
  id: string;
  fromSession: string;
  toSession: string;
  triggerType: 'manual' | 'automatic' | 'conditional';
  condition?: string;
  dataMapping: DataMapping[];
  status: 'pending' | 'completed' | 'failed';
  executedAt?: Date;
}

export interface DataMapping {
  sourceField: string;
  targetField: string;
  transform?: 'raw' | 'summary' | 'structured' | 'executable';
  filterCriteria?: Record<string, any>;
}

export interface TransitionContext {
  sourceSession: SessionReference;
  targetSession: SessionReference;
  transferredData: any;
  userPrompt?: string;
  metadata: Record<string, any>;
}

class CrossSessionOrchestrator {
  private workflows: Map<string, CrossSessionWorkflow> = new Map();

  /**
   * Create a new cross-session workflow
   */
  async createWorkflow(options: {
    name: string;
    description: string;
    initialSessionType: 'brainstorm' | 'claude' | 'orchestra';
    projectPath?: string;
    metadata?: Record<string, any>;
  }): Promise<CrossSessionWorkflow> {
    const workflowId = `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const workflow: CrossSessionWorkflow = {
      id: workflowId,
      name: options.name,
      description: options.description,
      sessions: [],
      transitions: [],
      status: 'draft',
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: options.metadata || {}
    };

    // Create initial session
    const initialSession = await this.createInitialSession(
      options.initialSessionType,
      options.projectPath,
      workflowId
    );
    
    workflow.sessions.push(initialSession);
    workflow.status = 'active';
    
    this.workflows.set(workflowId, workflow);
    
    // Store in backend
    try {
      await invoke('initialize_session_orchestration', {
        sessionId: workflowId,
        sessionType: 'workflow',
        orchestrationPreferences: {
          initialSessionType: options.initialSessionType,
          projectPath: options.projectPath,
          metadata: options.metadata
        }
      });
    } catch (error) {
      console.error('Failed to initialize workflow in backend:', error);
    }

    return workflow;
  }

  /**
   * Add a session to an existing workflow
   */
  async addSessionToWorkflow(
    workflowId: string,
    sessionType: 'brainstorm' | 'claude' | 'orchestra',
    options: {
      name?: string;
      projectPath?: string;
      metadata?: Record<string, any>;
    } = {}
  ): Promise<SessionReference> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }

    const session = await this.createInitialSession(sessionType, options.projectPath, workflowId);
    workflow.sessions.push(session);
    workflow.updatedAt = new Date();

    return session;
  }

  /**
   * Execute transition from brainstorm to Claude session
   */
  async transitionBrainstormToClaude(
    workflowId: string,
    brainstormSessionId: string,
    options: {
      selectedIdeas?: string[];
      projectPath: string;
      claudeModel?: ModelType;
      generationPrompt?: string;
    }
  ): Promise<SessionReference> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }

    // Get brainstorm session data
    const brainstormSession = await invoke<BrainstormSession>('get_brainstorm_session', { 
      sessionId: brainstormSessionId 
    });
    if (!brainstormSession) {
      throw new Error(`Brainstorm session ${brainstormSessionId} not found`);
    }

    // Extract ideas for transition
    const ideas = options.selectedIdeas 
      ? brainstormSession.ideas.filter((idea: any) => options.selectedIdeas!.includes(idea.id))
      : brainstormSession.ideas;

    // Create Claude session
    const claudeSession = await invoke<SessionData>('create_claude_session', {
      projectPath: options.projectPath,
      sessionType: 'claude',
      model: options.claudeModel || 'claude-3-5-sonnet-20241022',
      workflowId
    });

    // Create session reference
    const sessionRef: SessionReference = {
      id: claudeSession.id,
      type: 'claude',
      name: `Claude Session - ${brainstormSession.title}`,
      projectPath: options.projectPath,
      status: 'active',
      metadata: {
        sourceWorkflow: workflowId,
        sourceBrainstormSession: brainstormSessionId,
        transferredIdeas: ideas.length
      },
      createdAt: new Date()
    };

    // Generate implementation prompt from ideas
    const implementationPrompt = this.generateImplementationPrompt(ideas, options.generationPrompt);
    
    // Send prompt to Claude session
    await invoke('send_claude_prompt', {
      sessionId: claudeSession.id,
      prompt: implementationPrompt,
      model: options.claudeModel
    });

    // Create transition record
    const transition: WorkflowTransition = {
      id: `transition_${Date.now()}`,
      fromSession: brainstormSessionId,
      toSession: claudeSession.id,
      triggerType: 'manual',
      dataMapping: [
        {
          sourceField: 'ideas',
          targetField: 'prompt',
          transform: 'executable'
        }
      ],
      status: 'completed',
      executedAt: new Date()
    };

    workflow.sessions.push(sessionRef);
    workflow.transitions.push(transition);
    workflow.updatedAt = new Date();

    // Link sessions in backend
    try {
      await invoke('link_sessions', {
        sourceSessionId: brainstormSessionId,
        targetSessionId: claudeSession.id,
        linkType: 'implementation'
      });
    } catch (error) {
      console.error('Failed to link sessions in backend:', error);
    }

    return sessionRef;
  }

  /**
   * Execute transition from Claude to Orchestra session
   */
  async transitionClaudeToOrchestra(
    workflowId: string,
    claudeSessionId: string,
    options: {
      orchestrationMode: 'review' | 'parallel' | 'enhancement';
      agentCount?: number;
      specializations?: string[];
    }
  ): Promise<SessionReference> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }

    // Get Claude session context
    const claudeSessionData = await invoke<SessionData>('get_session_data', {
      sessionId: claudeSessionId
    });
    if (!claudeSessionData || claudeSessionData.id !== claudeSessionId) {
      throw new Error(`Claude session ${claudeSessionId} not active`);
    }

    // Create Orchestra session reference
    const orchestraSessionId = `orchestra_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const sessionRef: SessionReference = {
      id: orchestraSessionId,
      type: 'orchestra',
      name: `Orchestra - ${claudeSessionData.settings.name}`,
      projectPath: claudeSessionData.projectPath,
      status: 'active',
      metadata: {
        sourceWorkflow: workflowId,
        sourceClaudeSession: claudeSessionId,
        orchestrationMode: options.orchestrationMode,
        agentCount: options.agentCount || 3
      },
      createdAt: new Date()
    };

    // Initialize orchestra session in backend
    try {
      await invoke('initialize_session_orchestration', {
        sessionId: orchestraSessionId,
        sessionType: 'orchestra',
        orchestrationPreferences: {
          mode: options.orchestrationMode,
          sourceSession: claudeSessionId,
          projectPath: claudeSessionData.projectPath,
          specializations: options.specializations || ['coding', 'testing', 'reviewing']
        }
      });

      // Link the sessions
      await invoke('link_sessions', {
        sourceSessionId: claudeSessionId,
        targetSessionId: orchestraSessionId,
        linkType: 'orchestration'
      });
    } catch (error) {
      console.error('Failed to initialize orchestra session:', error);
    }

    // Create transition record
    const transition: WorkflowTransition = {
      id: `transition_${Date.now()}`,
      fromSession: claudeSessionId,
      toSession: orchestraSessionId,
      triggerType: 'manual',
      dataMapping: [
        {
          sourceField: 'messages',
          targetField: 'context',
          transform: 'structured'
        },
        {
          sourceField: 'projectPath',
          targetField: 'projectPath',
          transform: 'raw'
        }
      ],
      status: 'completed',
      executedAt: new Date()
    };

    workflow.sessions.push(sessionRef);
    workflow.transitions.push(transition);
    workflow.updatedAt = new Date();

    return sessionRef;
  }

  /**
   * Execute full workflow: brainstorm → Claude → orchestra
   */
  async executeFullWorkflow(
    brainstormSessionId: string,
    options: {
      workflowName: string;
      projectPath: string;
      selectedIdeas?: string[];
      claudeModel?: ModelType;
      orchestrationMode?: 'review' | 'parallel' | 'enhancement';
    }
  ): Promise<CrossSessionWorkflow> {
    // Create workflow
    const workflow = await this.createWorkflow({
      name: options.workflowName,
      description: `Full workflow from brainstorm session ${brainstormSessionId}`,
      initialSessionType: 'brainstorm',
      projectPath: options.projectPath,
      metadata: {
        fullWorkflow: true,
        sourceBrainstormSession: brainstormSessionId
      }
    });

    // Transition to Claude
    const claudeSession = await this.transitionBrainstormToClaude(
      workflow.id,
      brainstormSessionId,
      {
        selectedIdeas: options.selectedIdeas,
        projectPath: options.projectPath,
        claudeModel: options.claudeModel
      }
    );

    // Transition to Orchestra  
    await this.transitionClaudeToOrchestra(
      workflow.id,
      claudeSession.id,
      {
        orchestrationMode: options.orchestrationMode || 'review'
      }
    );

    workflow.status = 'active';
    return workflow;
  }

  /**
   * Get workflow status and progress
   */
  getWorkflowStatus(workflowId: string): CrossSessionWorkflow | null {
    return this.workflows.get(workflowId) || null;
  }

  /**
   * List all workflows
   */
  listWorkflows(): CrossSessionWorkflow[] {
    return Array.from(this.workflows.values());
  }

  /**
   * Archive completed workflow
   */
  archiveWorkflow(workflowId: string): void {
    const workflow = this.workflows.get(workflowId);
    if (workflow) {
      workflow.status = 'archived';
      workflow.updatedAt = new Date();
    }
  }

  // Private helper methods

  private async createInitialSession(
    sessionType: 'brainstorm' | 'claude' | 'orchestra',
    projectPath?: string,
    workflowId?: string
  ): Promise<SessionReference> {
    const sessionId = `${sessionType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      id: sessionId,
      type: sessionType,
      name: `${sessionType.charAt(0).toUpperCase() + sessionType.slice(1)} Session`,
      projectPath,
      status: 'active',
      metadata: {
        workflowId,
        initialSession: true
      },
      createdAt: new Date()
    };
  }

  private generateImplementationPrompt(ideas: any[], customPrompt?: string): string {
    if (customPrompt) {
      return customPrompt;
    }

    const ideaSummaries = ideas.map(idea => 
      `- ${idea.content}`
    ).join('\n');

    return `I have brainstormed the following ideas for implementation:

${ideaSummaries}

Please help me implement these ideas. Provide:
1. A clear project structure and architecture
2. Step-by-step implementation plan
3. Code examples for key components
4. Best practices and recommendations

Focus on creating a solid foundation that incorporates all the brainstormed concepts effectively.`;
  }

}

// Export singleton instance
export const crossSessionOrchestrator = new CrossSessionOrchestrator();