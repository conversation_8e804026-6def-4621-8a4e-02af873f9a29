import { invoke } from '@tauri-apps/api/core';
import { EventEmitter } from 'events';
import { Idea, IdeaCluster, BrainstormMessage } from '@/types/brainstorm';

export interface CollaborationUser {
  id: string;
  name: string;
  color?: string;
  cursor?: {
    x: number;
    y: number;
    viewType: string;
  };
  selection?: {
    ideaIds: string[];
    viewType: string;
  };
}

export interface CollaborationState {
  sessionId: string;
  isConnected: boolean;
  users: Map<string, CollaborationUser>;
  localUserId: string;
  localUserName: string;
}

export interface CollaborationEvents {
  userJoined: (user: CollaborationUser) => void;
  userLeft: (userId: string) => void;
  ideaCreated: (idea: Idea) => void;
  ideaUpdated: (idea: Idea) => void;
  ideaDeleted: (ideaId: string) => void;
  cursorMoved: (userId: string, position: { x: number; y: number; viewType: string }) => void;
  selectionChanged: (userId: string, selection: { ideaIds: string[]; viewType: string }) => void;
  chatMessage: (userId: string, message: string) => void;
  sessionSync: (fullState: any) => void;
  connectionStateChanged: (isConnected: boolean) => void;
}

export class BrainstormCollaborationClient extends EventEmitter {
  private ws: WebSocket | null = null;
  private state: CollaborationState;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private serverPort: number = 8765;

  constructor() {
    super();
    this.state = {
      sessionId: '',
      isConnected: false,
      users: new Map(),
      localUserId: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      localUserName: 'Anonymous User',
    };
  }

  // Start the WebSocket server (host mode)
  async startServer(port: number = 8765): Promise<void> {
    this.serverPort = port;
    try {
      const result = await invoke<string>('start_collaboration_server', { port });
      console.log(result);
    } catch (error) {
      throw new Error(`Failed to start collaboration server: ${error}`);
    }
  }

  // Stop the WebSocket server
  async stopServer(): Promise<void> {
    try {
      await invoke('stop_collaboration_server');
    } catch (error) {
      console.error('Failed to stop collaboration server:', error);
    }
  }

  // Connect to a collaboration session
  async connect(sessionId: string, userName: string, serverUrl?: string): Promise<void> {
    this.state.sessionId = sessionId;
    this.state.localUserName = userName;

    const url = serverUrl || `ws://localhost:${this.serverPort}`;
    
    try {
      this.ws = new WebSocket(url);
      
      this.ws.onopen = () => {
        this.state.isConnected = true;
        this.emit('connectionStateChanged', true);
        
        // Send join message
        this.send({
          type: 'Join',
          session_id: sessionId,
          user_id: this.state.localUserId,
          user_name: userName,
        });
      };

      this.ws.onmessage = (event) => {
        this.handleMessage(JSON.parse(event.data));
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };

      this.ws.onclose = () => {
        this.state.isConnected = false;
        this.emit('connectionStateChanged', false);
        this.attemptReconnect();
      };
    } catch (error) {
      throw new Error(`Failed to connect to collaboration server: ${error}`);
    }
  }

  // Disconnect from the session
  disconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      // Send leave message
      this.send({
        type: 'Leave',
        session_id: this.state.sessionId,
        user_id: this.state.localUserId,
      });

      this.ws.close();
    }

    this.ws = null;
    this.state.isConnected = false;
    this.state.users.clear();
    this.emit('connectionStateChanged', false);
  }

  // Send idea updates
  sendIdeaUpdate(idea: Idea, action: 'create' | 'update' | 'delete'): void {
    this.send({
      type: 'IdeaUpdate',
      session_id: this.state.sessionId,
      idea,
      action,
    });
  }

  // Send cursor position
  sendCursorPosition(x: number, y: number, viewType: string): void {
    this.send({
      type: 'CursorPosition',
      session_id: this.state.sessionId,
      user_id: this.state.localUserId,
      position: { x, y, view_type: viewType },
    });
  }

  // Send selection
  sendSelection(ideaIds: string[], viewType: string): void {
    this.send({
      type: 'Selection',
      session_id: this.state.sessionId,
      user_id: this.state.localUserId,
      selection: { idea_ids: ideaIds, view_type: viewType },
    });
  }

  // Send chat message
  sendChatMessage(message: string): void {
    this.send({
      type: 'Chat',
      session_id: this.state.sessionId,
      user_id: this.state.localUserId,
      message,
    });
  }

  // Send full session state (for syncing new users)
  sendSessionSync(fullState: any): void {
    this.send({
      type: 'SessionSync',
      session_id: this.state.sessionId,
      full_state: fullState,
    });
  }

  // Get current collaboration state
  getState(): CollaborationState {
    return { ...this.state, users: new Map(this.state.users) };
  }

  // Get list of connected users
  getUsers(): CollaborationUser[] {
    return Array.from(this.state.users.values());
  }

  // Get user by ID
  getUser(userId: string): CollaborationUser | undefined {
    return this.state.users.get(userId);
  }

  // Check if server is running
  async isServerRunning(): Promise<boolean> {
    try {
      const users = await invoke<[string, string][]>('get_collaboration_session_users', {
        sessionId: this.state.sessionId,
      });
      return true;
    } catch {
      return false;
    }
  }

  private send(message: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  private handleMessage(message: any): void {
    switch (message.type) {
      case 'Join':
        if (message.user_id !== this.state.localUserId) {
          const user: CollaborationUser = {
            id: message.user_id,
            name: message.user_name,
            color: this.generateUserColor(message.user_id),
          };
          this.state.users.set(message.user_id, user);
          this.emit('userJoined', user);
        }
        break;

      case 'Leave':
        if (this.state.users.has(message.user_id)) {
          this.state.users.delete(message.user_id);
          this.emit('userLeft', message.user_id);
        }
        break;

      case 'IdeaUpdate':
        switch (message.action) {
          case 'create':
            this.emit('ideaCreated', message.idea);
            break;
          case 'update':
            this.emit('ideaUpdated', message.idea);
            break;
          case 'delete':
            this.emit('ideaDeleted', message.idea.id);
            break;
        }
        break;

      case 'CursorPosition':
        if (this.state.users.has(message.user_id)) {
          const user = this.state.users.get(message.user_id)!;
          user.cursor = {
            x: message.position.x,
            y: message.position.y,
            viewType: message.position.view_type,
          };
          this.emit('cursorMoved', message.user_id, user.cursor);
        }
        break;

      case 'Selection':
        if (this.state.users.has(message.user_id)) {
          const user = this.state.users.get(message.user_id)!;
          user.selection = {
            ideaIds: message.selection.idea_ids,
            viewType: message.selection.view_type,
          };
          this.emit('selectionChanged', message.user_id, user.selection);
        }
        break;

      case 'Chat':
        this.emit('chatMessage', message.user_id, message.message);
        break;

      case 'SessionSync':
        this.emit('sessionSync', message.full_state);
        break;
    }
  }

  private attemptReconnect(): void {
    if (this.reconnectTimeout) return;

    this.reconnectTimeout = setTimeout(() => {
      this.reconnectTimeout = null;
      if (!this.state.isConnected && this.state.sessionId) {
        this.connect(this.state.sessionId, this.state.localUserName)
          .catch(error => {
            console.error('Reconnection failed:', error);
            this.attemptReconnect();
          });
      }
    }, 5000);
  }

  private generateUserColor(userId: string): string {
    // Generate a consistent color based on user ID
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57',
      '#FF9FF3', '#54A0FF', '#48DBFB', '#A29BFE', '#FD79A8',
    ];
    
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      hash = userId.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  }
}

// Create a singleton instance
export const collaborationClient = new BrainstormCollaborationClient();