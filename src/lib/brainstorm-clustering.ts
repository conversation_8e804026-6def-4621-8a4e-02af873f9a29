/**
 * AI-Powered Idea Clustering
 * 
 * Automatically groups related ideas using semantic similarity and AI analysis
 */

import { Idea, IdeaCluster } from '@/types/brainstorm';
import { invoke } from '@tauri-apps/api/core';

export interface ClusteringOptions {
  minClusterSize: number;
  similarityThreshold: number;
  maxClusters?: number;
  preserveExisting?: boolean;
}

export interface ClusteringSuggestion {
  clusterId: string;
  name: string;
  theme: string;
  ideaIds: string[];
  confidence: number;
  rationale: string;
}

const DEFAULT_OPTIONS: ClusteringOptions = {
  minClusterSize: 2,
  similarityThreshold: 0.7,
  maxClusters: 10,
  preserveExisting: true,
};

/**
 * Analyzes ideas and suggests clusters using AI
 */
export async function suggestClusters(
  ideas: Idea[],
  options: Partial<ClusteringOptions> = {}
): Promise<ClusteringSuggestion[]> {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  if (ideas.length < opts.minClusterSize) {
    return [];
  }

  try {
    // Prepare ideas for analysis
    const ideaData = ideas.map(idea => ({
      id: idea.id,
      content: idea.content,
      tags: idea.tags,
      status: idea.status,
      priority: idea.priority,
    }));

    // Call AI to analyze and cluster using the brainstorm chat API
    const prompt = `Analyze these brainstorming ideas and suggest logical groupings/clusters:

Ideas:
${ideaData.map((idea, i) => `${i + 1}. [${idea.id}] ${idea.content} (tags: ${idea.tags.join(', ')})`).join('\n')}

Requirements:
- Minimum cluster size: ${opts.minClusterSize} ideas
- Maximum clusters: ${opts.maxClusters || 'unlimited'}
- Group by semantic similarity, theme, or logical relationship
- Each idea can only belong to one cluster
- Provide a clear theme/name for each cluster

Return clusters as JSON array with format:
{
  "clusters": [
    {
      "name": "cluster name",
      "theme": "detailed theme description",
      "ideaIds": ["id1", "id2"],
      "confidence": 0.85,
      "rationale": "why these ideas belong together"
    }
  ]
}`;

    const response = await callBrainstormChatForClustering(prompt);
    
    // Parse AI response
    const result = parseClusteringResponse(response);
    
    // Validate and filter clusters
    return result.clusters
      .filter(cluster => 
        cluster.ideaIds.length >= opts.minClusterSize &&
        cluster.confidence >= opts.similarityThreshold
      )
      .map(cluster => ({
        clusterId: generateClusterId(),
        ...cluster,
      }));
      
  } catch (error) {
    console.error('Failed to suggest clusters:', error);
    return [];
  }
}

/**
 * Calculates semantic similarity between two ideas
 */
export function calculateSimilarity(idea1: Idea, idea2: Idea): number {
  // Simple similarity based on shared tags and keywords
  const tags1 = new Set(idea1.tags);
  const tags2 = new Set(idea2.tags);
  const sharedTags = [...tags1].filter(tag => tags2.has(tag)).length;
  const totalTags = tags1.size + tags2.size - sharedTags;
  
  const tagSimilarity = totalTags > 0 ? sharedTags / totalTags : 0;
  
  // Extract keywords from content
  const keywords1 = extractKeywords(idea1.content);
  const keywords2 = extractKeywords(idea2.content);
  const sharedKeywords = keywords1.filter(kw => keywords2.includes(kw)).length;
  const totalKeywords = new Set([...keywords1, ...keywords2]).size;
  
  const keywordSimilarity = totalKeywords > 0 ? sharedKeywords / totalKeywords : 0;
  
  // Combined similarity score
  return (tagSimilarity * 0.4 + keywordSimilarity * 0.6);
}

/**
 * Groups ideas by manual clustering algorithm (fallback)
 */
export function clusterIdeasManually(
  ideas: Idea[],
  options: Partial<ClusteringOptions> = {}
): IdeaCluster[] {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const clusters: IdeaCluster[] = [];
  const assigned = new Set<string>();
  
  ideas.forEach((idea, i) => {
    if (assigned.has(idea.id)) return;
    
    const cluster: IdeaCluster = {
      id: generateClusterId(),
      name: `Cluster ${clusters.length + 1}`,
      theme: '',
      ideaIds: [idea.id],
      color: generateClusterColor(clusters.length),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    assigned.add(idea.id);
    
    // Find similar ideas
    ideas.slice(i + 1).forEach(otherIdea => {
      if (assigned.has(otherIdea.id)) return;
      
      const similarity = calculateSimilarity(idea, otherIdea);
      if (similarity >= opts.similarityThreshold) {
        cluster.ideaIds.push(otherIdea.id);
        assigned.add(otherIdea.id);
      }
    });
    
    if (cluster.ideaIds.length >= opts.minClusterSize) {
      // Generate theme from common keywords
      const clusterIdeas = ideas.filter(i => cluster.ideaIds.includes(i.id));
      cluster.theme = generateClusterTheme(clusterIdeas);
      cluster.name = generateClusterName(cluster.theme);
      clusters.push(cluster);
    } else {
      // Unassign if cluster too small
      cluster.ideaIds.forEach(id => assigned.delete(id));
    }
  });
  
  return clusters;
}

/**
 * Re-clusters ideas, optionally preserving existing clusters
 */
export async function reclusterIdeas(
  ideas: Idea[],
  existingClusters: IdeaCluster[],
  options: Partial<ClusteringOptions> = {}
): Promise<IdeaCluster[]> {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  if (opts.preserveExisting) {
    // Only cluster unassigned ideas
    const assignedIds = new Set(
      existingClusters.flatMap(c => c.ideaIds)
    );
    const unassignedIdeas = ideas.filter(i => !assignedIds.has(i.id));
    
    if (unassignedIdeas.length >= opts.minClusterSize) {
      const newClusters = await suggestClusters(unassignedIdeas, opts);
      return [...existingClusters, ...newClusters.map(suggestion => ({
        id: suggestion.clusterId,
        name: suggestion.name,
        theme: suggestion.theme,
        ideaIds: suggestion.ideaIds,
        color: generateClusterColor(existingClusters.length + newClusters.indexOf(suggestion)),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }))];
    }
    
    return existingClusters;
  } else {
    // Complete re-clustering
    const suggestions = await suggestClusters(ideas, opts);
    return suggestions.map((suggestion, i) => ({
      id: suggestion.clusterId,
      name: suggestion.name,
      theme: suggestion.theme,
      ideaIds: suggestion.ideaIds,
      color: generateClusterColor(i),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }));
  }
}

// Utility functions

function parseClusteringResponse(response: string): { clusters: Omit<ClusteringSuggestion, 'clusterId'>[] } {
  try {
    // Extract JSON from response
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in response');
    }
    
    const parsed = JSON.parse(jsonMatch[0]);
    return parsed;
  } catch (error) {
    console.error('Failed to parse clustering response:', error);
    return { clusters: [] };
  }
}

function extractKeywords(text: string): string[] {
  // Simple keyword extraction - can be enhanced with NLP
  const words = text.toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 3);
  
  // Remove common words
  const stopWords = new Set(['that', 'this', 'what', 'when', 'where', 'which', 'with', 'about', 'after', 'before', 'during']);
  return words.filter(word => !stopWords.has(word));
}

function generateClusterTheme(ideas: Idea[]): string {
  // Extract common keywords
  const allKeywords = ideas.flatMap(idea => extractKeywords(idea.content));
  const keywordCounts = allKeywords.reduce((acc, kw) => {
    acc[kw] = (acc[kw] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  // Get top keywords
  const topKeywords = Object.entries(keywordCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 3)
    .map(([kw]) => kw);
  
  return `Ideas related to ${topKeywords.join(', ')}`;
}

function generateClusterName(theme: string): string {
  // Generate concise name from theme
  const keywords = theme.split(' ').filter(w => w.length > 3);
  return keywords.slice(0, 2).map(w => 
    w.charAt(0).toUpperCase() + w.slice(1)
  ).join(' ');
}

function generateClusterId(): string {
  return `cluster_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function generateClusterColor(index: number): string {
  const colors = [
    '#8B5CF6', // Violet
    '#3B82F6', // Blue
    '#10B981', // Emerald
    '#F59E0B', // Amber
    '#EF4444', // Red
    '#EC4899', // Pink
    '#14B8A6', // Teal
    '#6366F1', // Indigo
    '#84CC16', // Lime
    '#F97316', // Orange
  ];
  
  return colors[index % colors.length];
}

/**
 * Calls the brainstorm chat API for clustering analysis
 */
async function callBrainstormChatForClustering(prompt: string): Promise<string> {
  return new Promise(async (resolve, reject) => {
    const sessionId = `clustering_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    let accumulatedResponse = '';
    let timeoutId: NodeJS.Timeout;

    // Set up event listeners for the streaming response
    const { listen } = await import('@tauri-apps/api/event');
    
    const streamUnlisten = await listen<{ session_id: string; content: string; accumulated: string }>('chat-stream', (event) => {
      if (event.payload.session_id === sessionId) {
        accumulatedResponse = event.payload.accumulated;
        // Reset timeout on each chunk received
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          reject(new Error('Clustering request timed out'));
        }, 30000); // 30 second timeout
      }
    });

    const completeUnlisten = await listen<{ session_id: string; content: string }>('chat-complete', (event) => {
      if (event.payload.session_id === sessionId) {
        clearTimeout(timeoutId);
        streamUnlisten();
        completeUnlisten();
        errorUnlisten();
        resolve(event.payload.content);
      }
    });

    const errorUnlisten = await listen<{ session_id: string; error: string }>('chat-error', (event) => {
      if (event.payload.session_id === sessionId) {
        clearTimeout(timeoutId);
        streamUnlisten();
        completeUnlisten();
        errorUnlisten();
        reject(new Error(`Brainstorm chat error: ${event.payload.error}`));
      }
    });

    // Set initial timeout
    timeoutId = setTimeout(() => {
      streamUnlisten();
      completeUnlisten();
      errorUnlisten();
      reject(new Error('Clustering request timed out'));
    }, 30000);

    try {
      // Call the brainstorm chat command
      await invoke('brainstorm_chat', {
        request: {
          sessionId,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          model: 'claude-3-sonnet-20240229', // Default model for clustering
          temperature: 0.3, // Lower temperature for more consistent clustering
          maxTokens: 4000
        }
      });
    } catch (error) {
      clearTimeout(timeoutId);
      streamUnlisten();
      completeUnlisten();
      errorUnlisten();
      reject(error);
    }
  });
}