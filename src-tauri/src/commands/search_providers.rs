use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;
use scraper::{Html, Selector};

#[derive(Debug, Serialize, Deserialize)]
pub struct SearchResult {
    pub title: String,
    pub url: String,
    pub snippet: String,
    pub date: Option<String>,
    pub source: Option<String>,
    pub provider: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SearchStats {
    pub total_searches: u64,
    pub provider_usage: HashMap<String, u64>,
    pub failure_rate: HashMap<String, f64>,
}

// DuckDuckGo search without API key
#[tauri::command]
pub async fn search_duckduckgo(
    query: String,
    max_results: usize,
) -> Result<Vec<SearchResult>, String> {
    let encoded_query = urlencoding::encode(&query);
    let url = format!("https://html.duckduckgo.com/html/?q={}", encoded_query);
    
    // Make request with proper user agent
    let client = reqwest::Client::builder()
        .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        .build()
        .map_err(|e| format!("Failed to create HTTP client: {}", e))?;
    
    let response = client
        .get(&url)
        .send()
        .await
        .map_err(|e| format!("Failed to fetch search results: {}", e))?;
    
    if !response.status().is_success() {
        return Err(format!("Search request failed with status: {}", response.status()));
    }
    
    let html = response
        .text()
        .await
        .map_err(|e| format!("Failed to read response: {}", e))?;
    
    // Parse HTML results
    let document = Html::parse_document(&html);
    let result_selector = Selector::parse(".result").unwrap();
    let title_selector = Selector::parse(".result__title").unwrap();
    let snippet_selector = Selector::parse(".result__snippet").unwrap();
    let url_selector = Selector::parse(".result__url").unwrap();
    
    let mut results = Vec::new();
    
    for (i, result) in document.select(&result_selector).enumerate() {
        if i >= max_results {
            break;
        }
        
        let title = result
            .select(&title_selector)
            .next()
            .map(|el| el.text().collect::<String>().trim().to_string())
            .unwrap_or_default();
        
        let snippet = result
            .select(&snippet_selector)
            .next()
            .map(|el| el.text().collect::<String>().trim().to_string())
            .unwrap_or_default();
        
        let url = result
            .select(&url_selector)
            .next()
            .and_then(|el| el.value().attr("href"))
            .map(|s| s.to_string())
            .unwrap_or_default();
        
        if !title.is_empty() && !url.is_empty() {
            results.push(SearchResult {
                title,
                url,
                snippet,
                date: None,
                source: None,
                provider: "DuckDuckGo".to_string(),
            });
        }
    }
    
    // Update search statistics
    update_search_stats("DuckDuckGo", true).await;
    
    Ok(results)
}

// Alternative DuckDuckGo implementation using their instant answer API
#[tauri::command]
pub async fn search_duckduckgo_instant(
    query: String,
) -> Result<serde_json::Value, String> {
    let encoded_query = urlencoding::encode(&query);
    let url = format!(
        "https://api.duckduckgo.com/?q={}&format=json&no_html=1",
        encoded_query
    );
    
    let response = reqwest::get(&url)
        .await
        .map_err(|e| format!("Failed to fetch instant answers: {}", e))?;
    
    if !response.status().is_success() {
        return Err(format!("Request failed with status: {}", response.status()));
    }
    
    let data = response
        .json::<serde_json::Value>()
        .await
        .map_err(|e| format!("Failed to parse response: {}", e))?;
    
    Ok(data)
}

// Save search provider configuration
#[tauri::command]
pub async fn save_search_provider_config(
    provider: String,
    config: String,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let config_dir = app_dir.join("search_providers");
    fs::create_dir_all(&config_dir)
        .map_err(|e| format!("Failed to create config directory: {}", e))?;
    
    let config_file = config_dir.join(format!("{}.json", provider));
    
    fs::write(&config_file, config)
        .map_err(|e| format!("Failed to write config file: {}", e))?;
    
    Ok(())
}

// Load search provider configuration
#[tauri::command]
pub async fn load_search_provider_config(
    provider: String,
    app_handle: tauri::AppHandle,
) -> Result<String, String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let config_file = app_dir
        .join("search_providers")
        .join(format!("{}.json", provider));
    
    if !config_file.exists() {
        return Err("Configuration not found".to_string());
    }
    
    let config = fs::read_to_string(&config_file)
        .map_err(|e| format!("Failed to read config file: {}", e))?;
    
    Ok(config)
}

// Get search statistics
#[tauri::command]
pub async fn get_search_stats(
    app_handle: tauri::AppHandle,
) -> Result<SearchStats, String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let stats_file = app_dir.join("search_stats.json");
    
    if !stats_file.exists() {
        return Ok(SearchStats {
            total_searches: 0,
            provider_usage: HashMap::new(),
            failure_rate: HashMap::new(),
        });
    }
    
    let stats_content = fs::read_to_string(&stats_file)
        .map_err(|e| format!("Failed to read stats file: {}", e))?;
    
    let stats: SearchStats = serde_json::from_str(&stats_content)
        .map_err(|e| format!("Failed to parse stats: {}", e))?;
    
    Ok(stats)
}

// Update search statistics
async fn update_search_stats(provider: &str, success: bool) {
    // This would update the search statistics file
    // For now, just log the search
    if success {
        log::info!("Successful search with provider: {}", provider);
    } else {
        log::warn!("Failed search with provider: {}", provider);
    }
}

// Search with Google Custom Search API (requires API key)
#[tauri::command]
pub async fn search_google_custom(
    query: String,
    api_key: String,
    search_engine_id: String,
    max_results: usize,
) -> Result<Vec<SearchResult>, String> {
    let params = [
        ("q", query.as_str()),
        ("key", api_key.as_str()),
        ("cx", search_engine_id.as_str()),
        ("num", &max_results.to_string()),
    ];
    
    let url = reqwest::Url::parse_with_params(
        "https://www.googleapis.com/customsearch/v1",
        &params,
    ).map_err(|e| format!("Failed to build URL: {}", e))?;
    
    let response = reqwest::get(url)
        .await
        .map_err(|e| format!("Failed to fetch search results: {}", e))?;
    
    if !response.status().is_success() {
        return Err(format!("Search request failed with status: {}", response.status()));
    }
    
    let data: serde_json::Value = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse response: {}", e))?;
    
    let mut results = Vec::new();
    
    if let Some(items) = data["items"].as_array() {
        for item in items.iter().take(max_results) {
            results.push(SearchResult {
                title: item["title"].as_str().unwrap_or("").to_string(),
                url: item["link"].as_str().unwrap_or("").to_string(),
                snippet: item["snippet"].as_str().unwrap_or("").to_string(),
                date: None,
                source: item["displayLink"].as_str().map(|s| s.to_string()),
                provider: "Google".to_string(),
            });
        }
    }
    
    update_search_stats("Google", true).await;
    
    Ok(results)
}

// Search academic papers using Semantic Scholar API (no key required)
#[tauri::command]
pub async fn search_semantic_scholar(
    query: String,
    max_results: usize,
) -> Result<Vec<SearchResult>, String> {
    let encoded_query = urlencoding::encode(&query);
    let url = format!(
        "https://api.semanticscholar.org/graph/v1/paper/search?query={}&limit={}",
        encoded_query, max_results
    );
    
    let response = reqwest::get(&url)
        .await
        .map_err(|e| format!("Failed to fetch search results: {}", e))?;
    
    if !response.status().is_success() {
        return Err(format!("Search request failed with status: {}", response.status()));
    }
    
    let data: serde_json::Value = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse response: {}", e))?;
    
    let mut results = Vec::new();
    
    if let Some(papers) = data["data"].as_array() {
        for paper in papers {
            let title = paper["title"].as_str().unwrap_or("").to_string();
            let paper_id = paper["paperId"].as_str().unwrap_or("");
            let url = format!("https://www.semanticscholar.org/paper/{}", paper_id);
            let snippet = paper["abstract"].as_str().unwrap_or("").to_string();
            let year = paper["year"].as_u64().map(|y| y.to_string());
            
            results.push(SearchResult {
                title,
                url,
                snippet: snippet.chars().take(200).collect::<String>() + "...",
                date: year,
                source: Some("Semantic Scholar".to_string()),
                provider: "Semantic Scholar".to_string(),
            });
        }
    }
    
    update_search_stats("Semantic Scholar", true).await;
    
    Ok(results)
}