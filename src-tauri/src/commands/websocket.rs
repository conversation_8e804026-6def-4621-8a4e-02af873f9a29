use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use tokio::net::{TcpListener, TcpStream};
use futures_util::{SinkExt, StreamExt};
use tokio_tungstenite::{accept_async, tungstenite::Message};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum CollaborationMessage {
    Join {
        session_id: String,
        user_id: String,
        user_name: String,
    },
    Leave {
        session_id: String,
        user_id: String,
    },
    IdeaUpdate {
        session_id: String,
        idea: serde_json::Value,
        action: String, // "create", "update", "delete"
    },
    CursorPosition {
        session_id: String,
        user_id: String,
        position: CursorPos,
    },
    Selection {
        session_id: String,
        user_id: String,
        selection: SelectionData,
    },
    Chat {
        session_id: String,
        user_id: String,
        message: String,
    },
    SessionSync {
        session_id: String,
        full_state: serde_json::Value,
    },
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CursorPos {
    pub x: f64,
    pub y: f64,
    pub view_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SelectionData {
    pub idea_ids: Vec<String>,
    pub view_type: String,
}

#[derive(Debug, Clone)]
pub struct ConnectedUser {
    pub id: String,
    pub name: String,
    pub session_id: String,
    pub sender: Arc<Mutex<futures_util::stream::SplitSink<tokio_tungstenite::WebSocketStream<TcpStream>, Message>>>,
}

pub struct CollaborationServer {
    sessions: Arc<RwLock<HashMap<String, Vec<String>>>>, // session_id -> user_ids
    users: Arc<RwLock<HashMap<String, ConnectedUser>>>, // user_id -> user
    server_handle: Option<tokio::task::JoinHandle<()>>,
}

impl CollaborationServer {
    pub fn new() -> Self {
        Self {
            sessions: Arc::new(RwLock::new(HashMap::new())),
            users: Arc::new(RwLock::new(HashMap::new())),
            server_handle: None,
        }
    }

    pub async fn start(&mut self, port: u16) -> Result<(), String> {
        let addr = format!("127.0.0.1:{}", port);
        let listener = TcpListener::bind(&addr)
            .await
            .map_err(|e| format!("Failed to bind to {}: {}", addr, e))?;

        let sessions = self.sessions.clone();
        let users = self.users.clone();

        let handle = tokio::spawn(async move {
            while let Ok((stream, _)) = listener.accept().await {
                tokio::spawn(handle_connection(stream, sessions.clone(), users.clone()));
            }
        });

        self.server_handle = Some(handle);
        Ok(())
    }

    pub async fn stop(&mut self) -> Result<(), String> {
        if let Some(handle) = self.server_handle.take() {
            handle.abort();
        }
        Ok(())
    }

    pub async fn get_session_users(&self, session_id: &str) -> Vec<(String, String)> {
        let sessions = self.sessions.read().await;
        let users = self.users.read().await;
        
        if let Some(user_ids) = sessions.get(session_id) {
            user_ids.iter()
                .filter_map(|uid| users.get(uid).map(|u| (u.id.clone(), u.name.clone())))
                .collect()
        } else {
            Vec::new()
        }
    }
}

async fn handle_connection(
    stream: TcpStream,
    sessions: Arc<RwLock<HashMap<String, Vec<String>>>>,
    users: Arc<RwLock<HashMap<String, ConnectedUser>>>,
) {
    let ws_stream = match accept_async(stream).await {
        Ok(ws) => ws,
        Err(e) => {
            eprintln!("WebSocket connection error: {}", e);
            return;
        }
    };

    let (sender, mut receiver) = ws_stream.split();
    let sender = Arc::new(Mutex::new(sender));
    let mut user_id: Option<String> = None;

    while let Some(msg) = receiver.next().await {
        if let Ok(msg) = msg {
            if let Ok(text) = msg.to_text() {
                if let Ok(collab_msg) = serde_json::from_str::<CollaborationMessage>(text) {
                    match &collab_msg {
                        CollaborationMessage::Join { session_id, user_id: uid, user_name } => {
                            user_id = Some(uid.clone());
                            
                            // Add user to session
                            {
                                let mut sessions = sessions.write().await;
                                sessions.entry(session_id.clone())
                                    .or_insert_with(Vec::new)
                                    .push(uid.clone());
                            }

                            // Add user to users map
                            {
                                let mut users = users.write().await;
                                users.insert(uid.clone(), ConnectedUser {
                                    id: uid.clone(),
                                    name: user_name.clone(),
                                    session_id: session_id.clone(),
                                    sender: sender.clone(),
                                });
                            }

                            // Broadcast join to other users in session
                            broadcast_to_session(
                                &session_id,
                                &uid,
                                &collab_msg,
                                &sessions,
                                &users,
                            ).await;
                        }
                        CollaborationMessage::Leave { session_id, user_id: uid } => {
                            // Remove user from session
                            {
                                let mut sessions = sessions.write().await;
                                if let Some(users) = sessions.get_mut(&session_id) {
                                    users.retain(|u| u != &uid);
                                }
                            }

                            // Remove user from users map
                            {
                                let mut users = users.write().await;
                                users.remove(&uid);
                            }

                            // Broadcast leave to other users
                            broadcast_to_session(
                                &session_id,
                                &uid,
                                &collab_msg,
                                &sessions,
                                &users,
                            ).await;
                        }
                        CollaborationMessage::IdeaUpdate { session_id, .. } |
                        CollaborationMessage::CursorPosition { session_id, .. } |
                        CollaborationMessage::Selection { session_id, .. } |
                        CollaborationMessage::Chat { session_id, .. } |
                        CollaborationMessage::SessionSync { session_id, .. } => {
                            // Broadcast to all other users in the session
                            if let Some(uid) = &user_id {
                                broadcast_to_session(
                                    session_id,
                                    uid,
                                    &collab_msg,
                                    &sessions,
                                    &users,
                                ).await;
                            }
                        }
                    }
                }
            }
        } else {
            break;
        }
    }

    // Clean up on disconnect
    if let Some(uid) = user_id {
        let mut sessions = sessions.write().await;
        let mut users = users.write().await;
        
        if let Some(user) = users.remove(&uid) {
            if let Some(session_users) = sessions.get_mut(&user.session_id) {
                session_users.retain(|u| u != &uid);
            }
        }
    }
}

async fn broadcast_to_session(
    session_id: &str,
    sender_id: &str,
    message: &CollaborationMessage,
    sessions: &Arc<RwLock<HashMap<String, Vec<String>>>>,
    users: &Arc<RwLock<HashMap<String, ConnectedUser>>>,
) {
    let sessions = sessions.read().await;
    let users = users.read().await;
    
    if let Some(user_ids) = sessions.get(session_id) {
        let msg_text = serde_json::to_string(message).unwrap();
        
        for uid in user_ids {
            if uid != sender_id {
                if let Some(user) = users.get(uid) {
                    let mut sender = user.sender.lock().await;
                    let _ = sender.send(Message::text(msg_text.clone())).await;
                }
            }
        }
    }
}

// Tauri commands
use tauri::State;
use std::sync::Mutex as StdMutex;

pub struct WebSocketServerState {
    server: StdMutex<Option<CollaborationServer>>,
}

impl Default for WebSocketServerState {
    fn default() -> Self {
        Self {
            server: StdMutex::new(None),
        }
    }
}

#[tauri::command]
pub async fn start_collaboration_server(
    port: u16,
    state: State<'_, WebSocketServerState>,
) -> Result<String, String> {
    let mut server_guard = state.server.lock().unwrap();
    
    if server_guard.is_some() {
        return Err("Server is already running".to_string());
    }

    let mut server = CollaborationServer::new();
    server.start(port).await?;
    *server_guard = Some(server);

    Ok(format!("WebSocket server started on port {}", port))
}

#[tauri::command]
pub async fn stop_collaboration_server(
    state: State<'_, WebSocketServerState>,
) -> Result<(), String> {
    let mut server_guard = state.server.lock().unwrap();
    
    if let Some(mut server) = server_guard.take() {
        server.stop().await?;
    }

    Ok(())
}

#[tauri::command]
pub async fn get_collaboration_session_users(
    session_id: String,
    state: State<'_, WebSocketServerState>,
) -> Result<Vec<(String, String)>, String> {
    let server_guard = state.server.lock().unwrap();
    
    if let Some(server) = server_guard.as_ref() {
        Ok(server.get_session_users(&session_id).await)
    } else {
        Err("Server is not running".to_string())
    }
}