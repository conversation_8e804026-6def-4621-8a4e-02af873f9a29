use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;
use tauri::State;

#[derive(Debug, Serialize, Deserialize)]
pub struct IntegrationConfig {
    pub integration: String,
    pub config: String, // JSON string of the actual config
    pub created_at: String,
    pub updated_at: String,
}

// Save integration configuration securely
#[tauri::command]
pub async fn save_integration_config(
    integration: String,
    config: String,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let integrations_dir = app_dir.join("integrations");
    fs::create_dir_all(&integrations_dir)
        .map_err(|e| format!("Failed to create integrations directory: {}", e))?;
    
    let config_file = integrations_dir.join(format!("{}.json", integration));
    
    let integration_config = IntegrationConfig {
        integration: integration.clone(),
        config,
        created_at: chrono::Utc::now().to_rfc3339(),
        updated_at: chrono::Utc::now().to_rfc3339(),
    };
    
    let config_json = serde_json::to_string_pretty(&integration_config)
        .map_err(|e| format!("Failed to serialize config: {}", e))?;
    
    // TODO: Encrypt the config before saving for better security
    fs::write(&config_file, config_json)
        .map_err(|e| format!("Failed to write config file: {}", e))?;
    
    Ok(())
}

// Load integration configuration
#[tauri::command]
pub async fn load_integration_config(
    integration: String,
    app_handle: tauri::AppHandle,
) -> Result<String, String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let config_file = app_dir
        .join("integrations")
        .join(format!("{}.json", integration));
    
    if !config_file.exists() {
        return Err("Integration configuration not found".to_string());
    }
    
    let config_content = fs::read_to_string(&config_file)
        .map_err(|e| format!("Failed to read config file: {}", e))?;
    
    let integration_config: IntegrationConfig = serde_json::from_str(&config_content)
        .map_err(|e| format!("Failed to parse config: {}", e))?;
    
    // TODO: Decrypt the config if it was encrypted
    Ok(integration_config.config)
}

// Delete integration configuration
#[tauri::command]
pub async fn delete_integration_config(
    integration: String,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let config_file = app_dir
        .join("integrations")
        .join(format!("{}.json", integration));
    
    if config_file.exists() {
        fs::remove_file(&config_file)
            .map_err(|e| format!("Failed to delete config file: {}", e))?;
    }
    
    Ok(())
}

// List all configured integrations
#[tauri::command]
pub async fn list_configured_integrations(
    app_handle: tauri::AppHandle,
) -> Result<Vec<String>, String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let integrations_dir = app_dir.join("integrations");
    
    if !integrations_dir.exists() {
        return Ok(Vec::new());
    }
    
    let mut integrations = Vec::new();
    
    let entries = fs::read_dir(&integrations_dir)
        .map_err(|e| format!("Failed to read integrations directory: {}", e))?;
    
    for entry in entries {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let file_name = entry.file_name().to_string_lossy().to_string();
        
        if file_name.ends_with(".json") {
            let integration_name = file_name.trim_end_matches(".json");
            integrations.push(integration_name.to_string());
        }
    }
    
    Ok(integrations)
}

// Test integration connection
#[tauri::command]
pub async fn test_integration_connection(
    integration: String,
    config: String,
) -> Result<bool, String> {
    // This is a placeholder - actual implementation would depend on the integration type
    match integration.as_str() {
        "jira" => {
            // Parse Jira config and test connection
            // This would make an API call to Jira to verify credentials
            Ok(true)
        }
        "asana" => {
            // Parse Asana config and test connection
            Ok(true)
        }
        "trello" => {
            // Parse Trello config and test connection
            Ok(true)
        }
        _ => Err(format!("Unknown integration: {}", integration)),
    }
}